#!/bin/bash

# Script tổng hợp để chạy tất cả các test cho plugin AIDiBiz FPT Cloud
set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 AIDiBiz FPT Cloud Plugin - Complete Testing Suite${NC}"
echo -e "${BLUE}=================================================${NC}"

# Function to run a test and capture result
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "\n${YELLOW}📋 Running: $test_name${NC}"
    echo -e "${YELLOW}Command: $test_command${NC}"
    echo "----------------------------------------"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ $test_name: PASSED${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name: FAILED${NC}"
        return 1
    fi
}

# Test 1: Plugin Structure and Syntax
test_plugin_structure() {
    echo -e "\n${BLUE}🔍 PHASE 1: Plugin Structure and Syntax${NC}"
    echo "========================================"
    
    run_test "Simple Debug Test" "python3 simple_debug.py"
}

# Test 2: FPT Cloud API Connection
test_fpt_api() {
    echo -e "\n${BLUE}🔍 PHASE 2: FPT Cloud API Connection${NC}"
    echo "====================================="
    
    run_test "FPT Cloud API Test" "python3 test_client.py"
}

# Test 3: Plugin Package
test_plugin_package() {
    echo -e "\n${BLUE}🔍 PHASE 3: Plugin Package${NC}"
    echo "=========================="
    
    # Check if package exists
    if [ -f "aidibiz-fpt-cloud.signed.difypkg" ]; then
        echo -e "${GREEN}✅ Plugin package exists${NC}"
        
        # Check package size
        size=$(stat -f%z "aidibiz-fpt-cloud.signed.difypkg" 2>/dev/null || stat -c%s "aidibiz-fpt-cloud.signed.difypkg" 2>/dev/null)
        echo -e "${GREEN}✅ Package size: $size bytes${NC}"
        
        # Check if public key exists
        if [ -f "aidbiz_key_pair.public.pem" ]; then
            echo -e "${GREEN}✅ Public key exists${NC}"
            return 0
        else
            echo -e "${RED}❌ Public key missing${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ Plugin package missing${NC}"
        return 1
    fi
}

# Test 4: Dify Integration (if Dify is available)
test_dify_integration() {
    echo -e "\n${BLUE}🔍 PHASE 4: Dify Integration (Optional)${NC}"
    echo "======================================"
    
    # Check if Dify is running
    if curl -s http://localhost > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Dify is running${NC}"
        run_test "Dify Integration Test" "python3 test_dify_integration.py"
    else
        echo -e "${YELLOW}⚠️  Dify not running - skipping integration test${NC}"
        echo -e "${YELLOW}To test with Dify:${NC}"
        echo "1. Run: chmod +x setup_dify_test.sh && ./setup_dify_test.sh"
        echo "2. Then run: python3 test_dify_integration.py"
        return 0
    fi
}

# Generate test report
generate_report() {
    local total_tests=$1
    local passed_tests=$2
    
    echo -e "\n${BLUE}📊 FINAL TEST REPORT${NC}"
    echo -e "${BLUE}===================${NC}"
    
    echo -e "Plugin Name: AIDiBiz FPT Cloud"
    echo -e "Plugin Version: 1.0.0"
    echo -e "Test Date: $(date)"
    echo -e "Total Tests: $total_tests"
    echo -e "Passed Tests: $passed_tests"
    echo -e "Failed Tests: $((total_tests - passed_tests))"
    
    if [ $passed_tests -eq $total_tests ]; then
        echo -e "\n${GREEN}🎉 ALL TESTS PASSED!${NC}"
        echo -e "${GREEN}Plugin is ready for production deployment.${NC}"
        
        echo -e "\n${YELLOW}📦 Deployment Files:${NC}"
        echo "- Plugin Package: aidibiz-fpt-cloud.signed.difypkg"
        echo "- Public Key: aidbiz_key_pair.public.pem"
        echo "- Documentation: README.md"
        
        echo -e "\n${YELLOW}🚀 Next Steps:${NC}"
        echo "1. Deploy to Dify instance"
        echo "2. Configure FPT Cloud AI provider"
        echo "3. Test with real applications"
        
    else
        echo -e "\n${RED}⚠️  SOME TESTS FAILED${NC}"
        echo -e "${RED}Please fix the issues before deployment.${NC}"
    fi
}

# Main execution
main() {
    local total_tests=0
    local passed_tests=0
    
    # Phase 1: Plugin Structure
    if test_plugin_structure; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    # Phase 2: FPT API
    if test_fpt_api; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    # Phase 3: Plugin Package
    if test_plugin_package; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    # Phase 4: Dify Integration (optional)
    if test_dify_integration; then
        ((passed_tests++))
    fi
    ((total_tests++))
    
    # Generate report
    generate_report $total_tests $passed_tests
    
    # Return success if all critical tests passed (first 3 phases)
    if [ $passed_tests -ge 3 ]; then
        return 0
    else
        return 1
    fi
}

# Check dependencies
check_dependencies() {
    echo -e "${YELLOW}🔍 Checking dependencies...${NC}"
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python 3 not found${NC}"
        exit 1
    fi
    
    # Check required Python packages
    python3 -c "import openai, yaml, requests, dotenv" 2>/dev/null || {
        echo -e "${RED}❌ Missing Python packages. Installing...${NC}"
        pip3 install openai pyyaml requests python-dotenv
    }
    
    echo -e "${GREEN}✅ Dependencies OK${NC}"
}

# Run everything
echo -e "${YELLOW}Checking dependencies...${NC}"
check_dependencies

echo -e "\n${YELLOW}Starting comprehensive test suite...${NC}"
if main; then
    echo -e "\n${GREEN}🎉 Test suite completed successfully!${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Test suite failed!${NC}"
    exit 1
fi
