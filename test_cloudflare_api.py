#!/usr/bin/env python3
"""
Test script for Cloudflare Workers AI API
"""

import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_cloudflare_token():
    """Test Cloudflare API token verification"""
    api_token = "jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd"  # From task description
    
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json"
    }
    
    try:
        with httpx.Client() as client:
            response = client.get(
                "https://api.cloudflare.com/client/v4/user/tokens/verify",
                headers=headers,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print("✅ API Token is valid!")
                    return True
                else:
                    print("❌ API Token validation failed")
                    return False
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_cloudflare_workers_ai():
    """Test Cloudflare Workers AI API with a simple request"""
    api_token = "jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd"
    account_id = os.getenv("CLOUDFLARE_ACCOUNT_ID", "your-account-id-here")
    model = "@cf/qwen/qwq-32b-preview"
    
    if account_id == "your-account-id-here":
        print("⚠️  Please set CLOUDFLARE_ACCOUNT_ID environment variable")
        return False
    
    url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/ai/run/{model}"
    
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "max_tokens": 50,
        "temperature": 0.7
    }
    
    try:
        with httpx.Client() as client:
            response = client.post(
                url,
                headers=headers,
                json=payload,
                timeout=60.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    print("✅ Cloudflare Workers AI API is working!")
                    return True
                else:
                    print("❌ Unexpected response format")
                    return False
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Cloudflare Workers AI Plugin")
    print("=" * 50)
    
    print("\n1. Testing API Token Verification...")
    token_valid = test_cloudflare_token()
    
    if token_valid:
        print("\n2. Testing Cloudflare Workers AI API...")
        api_working = test_cloudflare_workers_ai()
        
        if api_working:
            print("\n🎉 All tests passed! Plugin should work correctly.")
        else:
            print("\n⚠️  API token is valid but Workers AI API test failed.")
            print("   This might be due to missing account ID or model access.")
    else:
        print("\n❌ API token validation failed. Please check your token.")
    
    print("\n" + "=" * 50)
    print("Test completed.")
