#!/usr/bin/env python3
"""
Test streaming functionality với FPT Cloud API
"""
import os
from openai import OpenAI

# Configuration
API_KEY = "sk-HMHa6NNBNnJdWfl_USuxuQ"
BASE_URL = "https://mkp-api.fptcloud.com"

def test_streaming_chat():
    """Test streaming chat với FPT Cloud API"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    models_to_test = ["QwQ-32B", "gemma-3-27b-it"]
    
    for model in models_to_test:
        print(f"\n{'='*60}")
        print(f"🚀 Testing Streaming with {model}")
        print(f"{'='*60}")
        
        # Test 1: Basic streaming
        print(f"\n📋 Test 1: Basic streaming chat")
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant. Give a brief response."
                    },
                    {
                        "role": "user",
                        "content": "Tell me a short joke about programming."
                    }
                ],
                stream=True,
                temperature=0.7,
                max_tokens=200
            )
            
            print(f"✅ Streaming started for {model}")
            print(f"📝 Response: ", end="", flush=True)
            
            full_response = ""
            chunk_count = 0
            
            for chunk in response:
                if chunk.choices and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta
                    if delta.content:
                        print(delta.content, end="", flush=True)
                        full_response += delta.content
                        chunk_count += 1
            
            print(f"\n\n✅ Streaming completed!")
            print(f"📊 Total chunks: {chunk_count}")
            print(f"📏 Total length: {len(full_response)} characters")
            
        except Exception as e:
            print(f"❌ Streaming failed for {model}: {str(e)}")
            
        # Test 2: Non-streaming for comparison
        print(f"\n📋 Test 2: Non-streaming (for comparison)")
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "user",
                        "content": "Tell me a short joke about programming."
                    }
                ],
                stream=False,
                temperature=0.7,
                max_tokens=200
            )
            
            print(f"✅ Non-streaming completed for {model}")
            print(f"📝 Response: {response.choices[0].message.content[:100]}...")
            
        except Exception as e:
            print(f"❌ Non-streaming failed for {model}: {str(e)}")

def test_streaming_with_function_calling():
    """Test streaming với function calling"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get weather information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {"type": "string"}
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    print(f"\n{'='*60}")
    print(f"🔧 Testing Streaming + Function Calling")
    print(f"{'='*60}")
    
    try:
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {
                    "role": "user",
                    "content": "What's the weather like in Hanoi? Please be brief."
                }
            ],
            tools=tools,
            tool_choice="none",  # Use our working approach
            stream=True,
            temperature=0.1
        )
        
        print(f"✅ Streaming + Function calling started")
        print(f"📝 Response: ", end="", flush=True)
        
        for chunk in response:
            if chunk.choices and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                if delta.content:
                    print(delta.content, end="", flush=True)
                    
                # Check for tool calls in streaming
                if delta.tool_calls:
                    print(f"\n🔧 Tool call detected in stream!")
                    for tool_call in delta.tool_calls:
                        if tool_call.function:
                            print(f"   Function: {tool_call.function.name}")
                            print(f"   Args: {tool_call.function.arguments}")
        
        print(f"\n\n✅ Streaming + Function calling completed!")
        
    except Exception as e:
        print(f"❌ Streaming + Function calling failed: {str(e)}")

def test_streaming_performance():
    """Test streaming performance"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    print(f"\n{'='*60}")
    print(f"⚡ Testing Streaming Performance")
    print(f"{'='*60}")
    
    import time
    
    # Test với prompt dài để thấy rõ streaming benefit
    long_prompt = "Write a detailed explanation about artificial intelligence, machine learning, and their applications in modern technology. Include examples and be comprehensive."
    
    # Test streaming
    print(f"\n📋 Streaming test...")
    start_time = time.time()
    first_chunk_time = None
    
    try:
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {
                    "role": "user",
                    "content": long_prompt
                }
            ],
            stream=True,
            temperature=0.1,
            max_tokens=500
        )
        
        chunk_count = 0
        for chunk in response:
            if chunk.choices and len(chunk.choices) > 0:
                delta = chunk.choices[0].delta
                if delta.content and first_chunk_time is None:
                    first_chunk_time = time.time()
                    print(f"⚡ First chunk received in: {first_chunk_time - start_time:.2f}s")
                    print(f"📝 Streaming: ", end="", flush=True)
                
                if delta.content:
                    print(".", end="", flush=True)
                    chunk_count += 1
        
        total_time = time.time() - start_time
        print(f"\n✅ Streaming completed!")
        print(f"📊 Stats:")
        print(f"   - First chunk: {first_chunk_time - start_time:.2f}s")
        print(f"   - Total time: {total_time:.2f}s")
        print(f"   - Total chunks: {chunk_count}")
        
    except Exception as e:
        print(f"❌ Streaming performance test failed: {str(e)}")

if __name__ == "__main__":
    print("🚀 FPT Cloud API Streaming Test")
    print("=" * 60)
    
    # Test basic streaming
    test_streaming_chat()
    
    # Test streaming with function calling
    test_streaming_with_function_calling()
    
    # Test streaming performance
    test_streaming_performance()
    
    print(f"\n{'='*60}")
    print("🎯 STREAMING CONCLUSION:")
    print("- Streaming should work out of the box với stream=True")
    print("- Real-time response cho better user experience")
    print("- Compatible với function calling")
    print("=" * 60)
