#!/usr/bin/env python3
"""
Test Cloudflare Workers AI plugin compliance with Dify standards
Based on official Dify documentation and Ollama/Anthropic examples
"""
import os

def test_required_functions():
    """Test if all required functions are present according to Dify docs"""
    print("🔍 Testing Required Functions (Dify Standards)...")
    
    # Test LLM file
    llm_file = "models/llm/llm.py"
    if not os.path.exists(llm_file):
        print(f"❌ {llm_file} not found")
        return False, ["LLM file missing"]
    
    with open(llm_file, 'r') as f:
        llm_content = f.read()
    
    # Required functions according to Dify documentation
    required_llm_functions = [
        # Core LLM functions (REQUIRED)
        'def _invoke',                          # Core invocation method
        'def validate_credentials',             # Credential validation
        'def get_num_tokens',                  # Token counting
        '_invoke_error_mapping',               # Error mapping property
        
        # Additional functions for full compliance
        'def get_model_mode',                  # Model mode (CHAT/COMPLETION)
        'def get_customizable_model_schema',   # Dynamic model configuration
        'def _num_tokens_from_messages',       # Internal token calculation
        
        # Class definition
        'class CloudflareWorkersAILanguageModel',
    ]
    
    missing_llm = []
    for func in required_llm_functions:
        if func in llm_content:
            print(f"✅ LLM: {func}")
        else:
            print(f"❌ LLM: {func}")
            missing_llm.append(func)
    
    # Test Text Embedding file
    embedding_file = "models/text_embedding/text_embedding.py"
    if not os.path.exists(embedding_file):
        print(f"❌ {embedding_file} not found")
        return False, ["Text embedding file missing"]
    
    with open(embedding_file, 'r') as f:
        embedding_content = f.read()
    
    # Required functions for Text Embedding
    required_embedding_functions = [
        # Core embedding functions (REQUIRED)
        'def _invoke',                          # Core invocation method
        'def validate_credentials',             # Credential validation
        'def get_num_tokens',                  # Token counting
        '_invoke_error_mapping',               # Error mapping property
        
        # Additional functions for full compliance
        'def get_customizable_model_schema',   # Dynamic model configuration
        
        # Class definition
        'class CloudflareWorkersAITextEmbeddingModel',
    ]
    
    missing_embedding = []
    for func in required_embedding_functions:
        if func in embedding_content:
            print(f"✅ Embedding: {func}")
        else:
            print(f"❌ Embedding: {func}")
            missing_embedding.append(func)
    
    all_missing = missing_llm + missing_embedding
    return len(all_missing) == 0, all_missing

def test_function_signatures():
    """Test if function signatures match Dify standards"""
    print("\n🔍 Testing Function Signatures (Dify Standards)...")
    
    llm_file = "models/llm/llm.py"
    with open(llm_file, 'r') as f:
        llm_content = f.read()
    
    # Check critical function signatures according to Dify docs
    signature_checks = [
        # LLM _invoke signature
        ('def _invoke(', 'LLM _invoke method'),
        ('model: str', '_invoke has model parameter'),
        ('credentials: dict', '_invoke has credentials parameter'),
        ('prompt_messages:', '_invoke has prompt_messages parameter'),
        ('model_parameters: dict', '_invoke has model_parameters parameter'),
        ('stream: bool = True', '_invoke has stream parameter'),
        
        # validate_credentials signature
        ('def validate_credentials(self, model: str, credentials: dict)', 'validate_credentials signature'),
        
        # get_num_tokens signature
        ('def get_num_tokens(', 'get_num_tokens method'),
        
        # get_model_mode signature
        ('def get_model_mode(self, model: str, credentials: dict)', 'get_model_mode signature'),
        
        # Error mapping property
        ('@property', '_invoke_error_mapping is property'),
        ('def _invoke_error_mapping(self)', '_invoke_error_mapping property method'),
    ]
    
    failed_signatures = []
    for pattern, description in signature_checks:
        if pattern in llm_content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description}")
            failed_signatures.append(description)
    
    return len(failed_signatures) == 0, failed_signatures

def test_implementation_details():
    """Test specific implementation details according to Dify standards"""
    print("\n🔍 Testing Implementation Details (Dify Standards)...")
    
    llm_file = "models/llm/llm.py"
    with open(llm_file, 'r') as f:
        llm_content = f.read()
    
    # Implementation checks based on Dify documentation
    implementation_checks = [
        # Return types and imports
        ('LLMMode.CHAT', 'get_model_mode returns LLMMode.CHAT'),
        ('LLMResult', 'Uses LLMResult for non-streaming'),
        ('Generator', 'Uses Generator for streaming'),
        ('CredentialsValidateFailedError', 'Uses proper credential validation errors'),
        
        # Error mapping structure
        ('InvokeConnectionError:', 'Maps connection errors'),
        ('InvokeAuthorizationError:', 'Maps authorization errors'),
        ('InvokeRateLimitError:', 'Maps rate limit errors'),
        ('InvokeServerUnavailableError:', 'Maps server errors'),
        ('InvokeBadRequestError:', 'Maps bad request errors'),
        
        # Enhanced features
        ('timeout_config', 'Has timeout configuration'),
        ('max_retries', 'Has retry logic'),
        ('httpx.Timeout', 'Uses proper HTTP timeout'),
        
        # Function calling support
        ('tools:', 'Supports function calling'),
        ('PromptMessageTool', 'Uses proper tool types'),
    ]
    
    failed_implementations = []
    for pattern, description in implementation_checks:
        if pattern in llm_content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description}")
            failed_implementations.append(description)
    
    return len(failed_implementations) == 0, failed_implementations

def test_provider_structure():
    """Test provider structure according to Dify standards"""
    print("\n🔍 Testing Provider Structure (Dify Standards)...")
    
    provider_py = "provider/cloudflare_workers_ai.py"
    provider_yaml = "provider/cloudflare-workers-ai.yaml"
    
    issues = []
    
    # Test provider Python file
    if os.path.exists(provider_py):
        print(f"✅ {provider_py}")
        with open(provider_py, 'r') as f:
            content = f.read()
        
        provider_checks = [
            ('class CloudflareWorkersAIProvider', 'Provider class exists'),
            ('def validate_provider_credentials', 'Provider credential validation'),
            ('ModelProvider', 'Inherits from ModelProvider'),
        ]
        
        for pattern, description in provider_checks:
            if pattern in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description}")
                issues.append(description)
    else:
        print(f"❌ {provider_py}")
        issues.append(f"Missing {provider_py}")
    
    # Test provider YAML file
    if os.path.exists(provider_yaml):
        print(f"✅ {provider_yaml}")
        with open(provider_yaml, 'r') as f:
            content = f.read()
        
        yaml_checks = [
            ('provider: cloudflare-workers-ai', 'Provider name correct'),
            ('label:', 'Has provider label'),
            ('description:', 'Has provider description'),
            ('icon:', 'Has provider icon'),
            ('supported_model_types:', 'Declares supported model types'),
            ('configurate_methods:', 'Has configuration methods'),
            ('provider_credential_schema:', 'Has credential schema'),
        ]
        
        for pattern, description in yaml_checks:
            if pattern in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description}")
                issues.append(description)
    else:
        print(f"❌ {provider_yaml}")
        issues.append(f"Missing {provider_yaml}")
    
    return len(issues) == 0, issues

def main():
    """Run all Dify standards compliance tests"""
    print("🚀 Cloudflare Workers AI Plugin - Dify Standards Compliance Test")
    print("=" * 70)
    print("📋 Based on official Dify documentation and Ollama/Anthropic examples")
    print("=" * 70)
    
    tests = [
        ("Required Functions", test_required_functions),
        ("Function Signatures", test_function_signatures),
        ("Implementation Details", test_implementation_details),
        ("Provider Structure", test_provider_structure),
    ]
    
    results = {}
    all_issues = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*25} {test_name} {'='*25}")
        try:
            success, issues = test_func()
            results[test_name] = success
            if not success:
                all_issues.extend(issues)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
            all_issues.append(f"{test_name}: {e}")
    
    # Summary
    print("\n" + "="*70)
    print("📊 DIFY STANDARDS COMPLIANCE SUMMARY")
    print("="*70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Plugin is FULLY COMPLIANT with Dify standards!")
        print("✅ All required functions implemented")
        print("✅ Function signatures match Dify expectations")
        print("✅ Implementation follows best practices")
        print("✅ Provider structure is correct")
        print("\n🚀 Plugin is ready for deployment and should successfully add models!")
        
        print("\n📋 Compliance Summary:")
        print("✅ Core LLM functions: _invoke, validate_credentials, get_num_tokens")
        print("✅ Error mapping: _invoke_error_mapping property")
        print("✅ Model mode: get_model_mode returns LLMMode.CHAT")
        print("✅ Dynamic configuration: get_customizable_model_schema")
        print("✅ Token calculation: _num_tokens_from_messages")
        print("✅ Text embedding support: Full implementation")
        print("✅ Provider validation: validate_provider_credentials")
        print("✅ Enhanced features: Timeout, retry, function calling")
        
    else:
        print("⚠️ Plugin needs fixes to be fully compliant")
        print("\n❌ Issues found:")
        for issue in all_issues:
            print(f"   - {issue}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
