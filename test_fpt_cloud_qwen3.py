#!/usr/bin/env python3
"""
Test FPT Cloud API với model Qwen3-32B
"""
import os
import json
import time
from openai import OpenAI

# FPT Cloud configuration (Updated)
FPT_API_KEY = "jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd"
FPT_BASE_URL = "https://mkp-api.fptcloud.com"  # Updated base URL
MODEL_NAME = "Qwen3-32B"  # Will test different model names

def test_basic_chat():
    """Test basic chat completion"""
    print("🔍 Testing Basic Chat with Qwen3-32B...")
    
    try:
        client = OpenAI(
            api_key=FPT_API_KEY,
            base_url=FPT_BASE_URL
        )
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "user", "content": "Hello! What is your name and what can you do?"}
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        print("✅ Basic Chat Response:")
        print(f"   Model: {response.model}")
        print(f"   Content: {response.choices[0].message.content}")
        print(f"   Usage: {response.usage}")
        return True
        
    except Exception as e:
        print(f"❌ Basic Chat Error: {e}")
        return False

def test_streaming_chat():
    """Test streaming chat completion"""
    print("\n🔍 Testing Streaming Chat with Qwen3-32B...")
    
    try:
        client = OpenAI(
            api_key=FPT_API_KEY,
            base_url=FPT_BASE_URL
        )
        
        stream = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "user", "content": "Count from 1 to 5 and explain each number."}
            ],
            max_tokens=150,
            temperature=0.7,
            stream=True
        )
        
        print("✅ Streaming Response:")
        full_content = ""
        for chunk in stream:
            if chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
                full_content += content
        
        print(f"\n   Total length: {len(full_content)} characters")
        return True
        
    except Exception as e:
        print(f"❌ Streaming Chat Error: {e}")
        return False

def test_function_calling():
    """Test function calling capability"""
    print("\n🔍 Testing Function Calling with Qwen3-32B...")
    
    try:
        client = OpenAI(
            api_key=FPT_API_KEY,
            base_url=FPT_BASE_URL
        )
        
        # Define a simple function
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get current weather for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "The city name"
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["celsius", "fahrenheit"],
                                "description": "Temperature unit"
                            }
                        },
                        "required": ["location"]
                    }
                }
            }
        ]
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "user", "content": "What's the weather like in Hanoi today?"}
            ],
            tools=tools,
            tool_choice="auto",
            max_tokens=200,
            temperature=0.7
        )
        
        print("✅ Function Calling Response:")
        print(f"   Model: {response.model}")
        
        if response.choices[0].message.tool_calls:
            print("   Tool calls detected:")
            for tool_call in response.choices[0].message.tool_calls:
                print(f"     Function: {tool_call.function.name}")
                print(f"     Arguments: {tool_call.function.arguments}")
        else:
            print("   No tool calls, regular response:")
            print(f"   Content: {response.choices[0].message.content}")
        
        print(f"   Usage: {response.usage}")
        return True
        
    except Exception as e:
        print(f"❌ Function Calling Error: {e}")
        return False

def test_vietnamese_language():
    """Test Vietnamese language support"""
    print("\n🔍 Testing Vietnamese Language Support...")
    
    try:
        client = OpenAI(
            api_key=FPT_API_KEY,
            base_url=FPT_BASE_URL
        )
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "user", "content": "Xin chào! Bạn có thể nói tiếng Việt không? Hãy giới thiệu về Việt Nam."}
            ],
            max_tokens=200,
            temperature=0.7
        )
        
        print("✅ Vietnamese Response:")
        print(f"   Content: {response.choices[0].message.content}")
        print(f"   Usage: {response.usage}")
        return True
        
    except Exception as e:
        print(f"❌ Vietnamese Test Error: {e}")
        return False

def test_complex_reasoning():
    """Test complex reasoning capability"""
    print("\n🔍 Testing Complex Reasoning...")
    
    try:
        client = OpenAI(
            api_key=FPT_API_KEY,
            base_url=FPT_BASE_URL
        )
        
        response = client.chat.completions.create(
            model=MODEL_NAME,
            messages=[
                {"role": "user", "content": """
Solve this step by step:
A train travels from city A to city B at 80 km/h and returns at 120 km/h. 
If the total journey time is 5 hours, what is the distance between the two cities?
Show your work.
                """}
            ],
            max_tokens=300,
            temperature=0.3
        )
        
        print("✅ Complex Reasoning Response:")
        print(f"   Content: {response.choices[0].message.content}")
        print(f"   Usage: {response.usage}")
        return True
        
    except Exception as e:
        print(f"❌ Complex Reasoning Error: {e}")
        return False

def test_model_info():
    """Test getting model information and try different model names"""
    print("\n🔍 Testing Model Information...")

    # Try different possible model names
    possible_models = [
        "Qwen3-32B",
        "qwen3-32b",
        "Qwen-32B",
        "qwen-32b",
        "QwQ-32B",
        "qwq-32b"
    ]

    client = OpenAI(
        api_key=FPT_API_KEY,
        base_url=FPT_BASE_URL
    )

    # Try to get models list
    try:
        models = client.models.list()
        print("✅ Available Models:")
        for model in models.data:
            print(f"   - {model.id}")
            if "qwen" in model.id.lower() or "qwq" in model.id.lower():
                print(f"     ⭐ Found Qwen/QwQ model: {model.id}")
    except Exception as e:
        print(f"⚠️ Could not list models: {e}")

    # Test each possible model name
    for model_name in possible_models:
        print(f"\n🧪 Testing model: {model_name}")
        try:
            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "user", "content": "Hello! What is your name?"}
                ],
                max_tokens=50,
                temperature=0.7
            )

            print(f"✅ Model {model_name} works!")
            print(f"   Response: {response.choices[0].message.content}")

            # Update global MODEL_NAME if this one works
            global MODEL_NAME
            MODEL_NAME = model_name
            return True

        except Exception as e:
            print(f"❌ Model {model_name} failed: {e}")
            continue

    print("❌ No working model found")
    return False

def main():
    """Run all tests for FPT Cloud Qwen3-32B"""
    print("🚀 FPT Cloud API Test - Qwen3-32B Model")
    print("=" * 60)
    print(f"🔑 API Key: {FPT_API_KEY[:20]}...")
    print(f"🌐 Base URL: {FPT_BASE_URL}")
    print(f"🤖 Model: {MODEL_NAME}")
    print("=" * 60)
    
    tests = [
        ("Model Information", test_model_info),
        ("Basic Chat", test_basic_chat),
        ("Streaming Chat", test_streaming_chat),
        ("Function Calling", test_function_calling),
        ("Vietnamese Language", test_vietnamese_language),
        ("Complex Reasoning", test_complex_reasoning),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
            time.sleep(1)  # Rate limiting
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 FPT CLOUD QWEN3-32B TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! FPT Cloud Qwen3-32B is working perfectly!")
    elif passed >= total * 0.8:
        print("✅ Most tests passed! FPT Cloud Qwen3-32B is working well.")
    else:
        print("⚠️ Some tests failed. Check the configuration or API status.")
    
    print(f"\n📋 Test Configuration:")
    print(f"   Model: {MODEL_NAME}")
    print(f"   API Key: {FPT_API_KEY[:20]}...")
    print(f"   Base URL: {FPT_BASE_URL}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
