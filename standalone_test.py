#!/usr/bin/env python3
"""
Standalone test cho AIDiBiz FPT Cloud Plugin
Không sử dụng dify_plugin để tránh conflict
"""

import os
import sys
import yaml
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_plugin_structure():
    """Test plugin structure"""
    print("🔍 Testing plugin structure...")
    
    # Check manifest.yaml
    try:
        with open("manifest.yaml", "r") as f:
            manifest = yaml.safe_load(f)
        
        print(f"✅ Plugin name: {manifest['name']}")
        print(f"✅ Plugin version: {manifest['version']}")
        print(f"✅ Plugin author: {manifest['author']}")
        
        return True
    except Exception as e:
        print(f"❌ Manifest error: {e}")
        return False

def test_provider_config():
    """Test provider configuration"""
    print("🔍 Testing provider configuration...")
    
    try:
        with open("provider/fpt-cloud.yaml", "r") as f:
            config = yaml.safe_load(f)
        
        print(f"✅ Provider: {config['provider']}")
        print(f"✅ Supported model types: {config['supported_model_types']}")
        print(f"✅ Configuration methods: {config['configurate_methods']}")

        # Check if has model credential schema for customizable models
        if 'model_credential_schema' in config:
            print(f"✅ Model credential schema available")

        # Check provider credential schema
        if 'provider_credential_schema' in config:
            print(f"✅ Provider credential schema available")
        
        return True
    except Exception as e:
        print(f"❌ Provider config error: {e}")
        return False

def test_api_connection():
    """Test API connection"""
    print("🔍 Testing FPT Cloud API...")
    
    api_key = os.getenv("API_KEY", "")
    if not api_key:
        print("❌ No API key found")
        return False
    
    try:
        from openai import OpenAI
        
        client = OpenAI(
            api_key=api_key,
            base_url=os.getenv("BASE_URL", "https://mkp-api.fptcloud.com")
        )
        
        # Test LLM
        print("Testing QwQ-32B...")
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[{"role": "user", "content": "Say 'OK'"}],
            max_tokens=5
        )
        print(f"✅ LLM response: {response.choices[0].message.content}")
        
        # Test Embedding
        print("Testing Vietnamese_Embedding...")
        emb_response = client.embeddings.create(
            model="Vietnamese_Embedding",
            input="Test"
        )
        print(f"✅ Embedding dimension: {len(emb_response.data[0].embedding)}")
        
        return True
        
    except Exception as e:
        print(f"❌ API test error: {e}")
        return False

def check_deployment_files():
    """Check deployment files"""
    print("🔍 Checking deployment files...")
    
    files = [
        "aidibiz_fpt_cloud.signed.difypkg",
        "aidbiz_key_pair.public.pem"
    ]
    
    all_exist = True
    for file_path in files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def main():
    """Main test function"""
    print("🚀 AIDiBiz FPT Cloud Plugin - Standalone Test")
    print("=" * 50)
    
    tests = [
        ("Plugin Structure", test_plugin_structure),
        ("Provider Config", test_provider_config),
        ("API Connection", test_api_connection),
        ("Deployment Files", check_deployment_files),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed!")
        print("\n📦 Plugin ready for deployment:")
        print("- File: aidibiz_fpt_cloud.signed.difypkg")
        print("- Public key: aidbiz_key_pair.public.pem")
        print("\n🚀 Upload to Dify and configure FPT Cloud AI provider!")
        return True
    else:
        print("\n❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
