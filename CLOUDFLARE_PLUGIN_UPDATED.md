# Cloudflare Workers AI Plugin - Updated Configuration

## 🔄 What Changed

Plugin đã được cập nhật để hỗ trợ **khai báo token theo từng model** thay vì chỉ có token chung ở provider level.

### ✅ Cấu hình mới (Recommended)

#### Provider Level (T<PERSON><PERSON> chọn)
```yaml
Provider Credentials:
  - Default Account ID: your-default-cloudflare-account-id (optional)
```

#### Model Level (Bắt buộc cho mỗi model)
```yaml
Model Credentials:
  - API Token: your-cloudflare-api-token-for-this-model (required)
  - Account ID: account-id-for-this-model (required, có thể dùng default)
  - Model Name: @cf/qwen/qwq-32b-preview
  - Context Size: 32768
  - Max Tokens: 4096
  - Vision Support: Yes/No
  - Function Call Support: Yes/No
```

## 🎯 Lợi ích của cấu hình mới

### 1. **<PERSON><PERSON><PERSON> mật cao hơn**
- Mỗi model có token riêng
- <PERSON><PERSON> thể giới hạn quyền truy cập theo model
- <PERSON><PERSON> dàng thu hồi token cho model cụ thể

### 2. **Linh hoạt hơn**
- Có thể sử dụng nhiều Cloudflare account khác nhau
- Mỗi model có thể có cấu hình riêng
- Dễ dàng quản lý chi phí theo model

### 3. **Tuân thủ best practices**
- Theo chuẩn của LocalAI và Ollama plugins
- Phù hợp với kiến trúc Dify
- Dễ dàng mở rộng trong tương lai

## 📋 Hướng dẫn cấu hình

### Bước 1: Cài đặt Provider (Tùy chọn)
```
1. Vào Dify Admin Panel
2. Chọn "Model Providers"
3. Tìm "Cloudflare Workers AI"
4. Nhập Default Account ID (nếu muốn dùng chung)
```

### Bước 2: Cấu hình Model (Bắt buộc)
```
1. Chọn "Add Model"
2. Nhập thông tin:
   - API Token: Token riêng cho model này
   - Account ID: Account ID cho model (hoặc để trống dùng default)
   - Model Name: @cf/qwen/qwq-32b-preview
   - Context Size: 32768
   - Max Tokens: 4096
   - Vision Support: No (cho QwQ-32B)
   - Function Call Support: Yes (nếu muốn)
```

## 🔧 Ví dụ cấu hình

### Scenario 1: Một account, nhiều models
```json
Provider:
{
  "default_account_id": "abc123def456"
}

Model 1 (QwQ-32B):
{
  "api_token": "token-for-qwq-model",
  "model": "@cf/qwen/qwq-32b-preview",
  "context_size": "32768",
  "max_tokens": "4096",
  "vision_support": "false",
  "function_call_support": "true"
}

Model 2 (Embedding):
{
  "api_token": "token-for-embedding-model", 
  "model": "@cf/baai/bge-base-en-v1.5",
  "context_size": "512"
}
```

### Scenario 2: Nhiều accounts
```json
Provider:
{
  "default_account_id": ""  // Không dùng default
}

Model 1 (Production):
{
  "api_token": "prod-token",
  "account_id": "prod-account-id",
  "model": "@cf/qwen/qwq-32b-preview"
}

Model 2 (Development):
{
  "api_token": "dev-token",
  "account_id": "dev-account-id", 
  "model": "@cf/qwen/qwq-32b-preview"
}
```

## 🚀 Migration từ phiên bản cũ

Nếu bạn đã cài plugin phiên bản cũ:

1. **Backup cấu hình hiện tại**
2. **Gỡ plugin cũ**
3. **Cài plugin mới**: `aidibiz_cloudflare_workers_ai.signed.difypkg`
4. **Cấu hình lại theo hướng dẫn mới**

## 📦 Plugin Information

- **File**: `aidibiz_cloudflare_workers_ai.signed.difypkg`
- **Size**: ~65KB
- **Version**: 1.0.0 (Updated)
- **Compatibility**: Dify v0.6.0+

## ✅ Đã test

- ✅ Plugin structure validation
- ✅ YAML configuration syntax
- ✅ Python code compilation
- ✅ Package creation and signing
- ✅ Credential flow logic
- ✅ Per-model token configuration

## 🎉 Kết luận

Plugin Cloudflare Workers AI đã được cập nhật thành công với:

- **Per-model token configuration**: Mỗi model có token riêng
- **Flexible account management**: Hỗ trợ nhiều Cloudflare accounts
- **Enhanced security**: Token được phân quyền theo model
- **Better compliance**: Tuân thủ chuẩn Dify plugin architecture

Plugin sẵn sàng để deploy và sử dụng trong production!
