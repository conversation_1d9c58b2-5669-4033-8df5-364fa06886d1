#!/bin/bash

# Script để đóng gói và ký plugin Dify
# Tác giả: AIDBiz Team
# Ngày: 20/05/2024

set -e  # Dừng script nếu có lỗi

# Màu sắc cho output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Thông tin plugin
PLUGIN_NAME="aidibiz_cloudflare_workers_ai"
PLUGIN_VERSION=$(grep -E "^version:" manifest.yaml | awk '{print $2}')
KEY_PAIR_NAME="aidbiz_key_pair"
DIFY_CLI="./dify"
DIFY_CLI_VERSION="0.1.0"

# Kiểm tra hệ điều hành
check_os() {
    echo -e "${YELLOW}Kiểm tra hệ điều hành...${NC}"
    
    OS="$(uname -s)"
    ARCH="$(uname -m)"
    
    case "${OS}" in
        Linux*)     OS_TYPE=linux;;
        Darwin*)    OS_TYPE=darwin;;
        *)          echo -e "${RED}Hệ điều hành không được hỗ trợ: ${OS}${NC}" && exit 1;;
    esac
    
    case "${ARCH}" in
        x86_64*)    ARCH_TYPE=amd64;;
        arm64*)     ARCH_TYPE=arm64;;
        *)          echo -e "${RED}Kiến trúc không được hỗ trợ: ${ARCH}${NC}" && exit 1;;
    esac
    
    echo -e "${GREEN}Đã phát hiện: ${OS_TYPE}-${ARCH_TYPE}${NC}"
}

# Tải công cụ Dify CLI
download_dify_cli() {
    if [ -f "$DIFY_CLI" ]; then
        echo -e "${GREEN}Công cụ Dify CLI đã tồn tại.${NC}"
        return
    fi
    
    echo -e "${YELLOW}Tải công cụ Dify CLI...${NC}"
    DOWNLOAD_URL="https://github.com/langgenius/dify-plugin-daemon/releases/download/${DIFY_CLI_VERSION}/dify-plugin-${OS_TYPE}-${ARCH_TYPE}"
    
    curl -L -o "$DIFY_CLI" "$DOWNLOAD_URL"
    chmod +x "$DIFY_CLI"
    
    echo -e "${GREEN}Đã tải công cụ Dify CLI thành công.${NC}"
}

# Tạo cặp khóa
generate_key_pair() {
    if [ -f "${KEY_PAIR_NAME}.private.pem" ] && [ -f "${KEY_PAIR_NAME}.public.pem" ]; then
        echo -e "${GREEN}Cặp khóa đã tồn tại.${NC}"
        return
    fi
    
    echo -e "${YELLOW}Tạo cặp khóa mới...${NC}"
    "$DIFY_CLI" signature generate -f "$KEY_PAIR_NAME"
    
    echo -e "${GREEN}Đã tạo cặp khóa thành công.${NC}"
}

# Đóng gói plugin
package_plugin() {
    echo -e "${YELLOW}Đóng gói plugin...${NC}"
    
    # Kiểm tra file .difyignore
    if [ ! -f ".difyignore" ]; then
        echo -e "${YELLOW}Tạo file .difyignore...${NC}"
        cat > .difyignore << EOF
__pycache__/
*.pyc
*.pyo
*.pyd

# Environment files
.env
.env.*

# System files
.DS_Store

# Virtual environments
.venv/
venv/

# Binary files
dify

# Git directory
.git/

# IDE files
.idea/

# Temporary files
*.tmp
*.bak

# Log files
*.log

# Package files
*.difypkg
EOF
    fi
    
    # Đóng gói plugin
    "$DIFY_CLI" plugin package . -o "${PLUGIN_NAME}.difypkg"
    
    echo -e "${GREEN}Đã đóng gói plugin thành công: ${PLUGIN_NAME}.difypkg${NC}"
}

# Ký plugin
sign_plugin() {
    echo -e "${YELLOW}Ký plugin...${NC}"
    
    "$DIFY_CLI" signature sign "${PLUGIN_NAME}.difypkg" -p "${KEY_PAIR_NAME}.private.pem"
    
    echo -e "${GREEN}Đã ký plugin thành công: ${PLUGIN_NAME}.signed.difypkg${NC}"
}

# Xác minh chữ ký
verify_signature() {
    echo -e "${YELLOW}Xác minh chữ ký...${NC}"
    
    "$DIFY_CLI" signature verify "${PLUGIN_NAME}.signed.difypkg" -p "${KEY_PAIR_NAME}.public.pem"
    
    echo -e "${GREEN}Đã xác minh chữ ký thành công.${NC}"
}

# Hiển thị hướng dẫn cài đặt
show_installation_guide() {
    echo -e "\n${GREEN}=== HƯỚNG DẪN CÀI ĐẶT PLUGIN ===${NC}"
    echo -e "${YELLOW}1. Cấu hình máy chủ Dify để chấp nhận chữ ký của bạn:${NC}"
    echo -e "   a. Tạo thư mục để lưu khóa công khai:"
    echo -e "      mkdir -p docker/volumes/plugin_daemon/public_keys"
    echo -e "      cp ${KEY_PAIR_NAME}.public.pem docker/volumes/plugin_daemon/public_keys"
    echo -e "\n   b. Cấu hình biến môi trường trong docker-compose.override.yaml:"
    echo -e "      services:"
    echo -e "        plugin_daemon:"
    echo -e "          environment:"
    echo -e "            FORCE_VERIFYING_SIGNATURE: true"
    echo -e "            THIRD_PARTY_SIGNATURE_VERIFICATION_ENABLED: true"
    echo -e "            THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS: /app/storage/public_keys/${KEY_PAIR_NAME}.public.pem"
    echo -e "\n   c. Khởi động lại dịch vụ Dify:"
    echo -e "      cd docker"
    echo -e "      docker compose down"
    echo -e "      docker compose up -d"
    echo -e "\n${YELLOW}2. Cài đặt plugin đã ký:${NC}"
    echo -e "   - Đăng nhập vào trang quản trị Dify"
    echo -e "   - Đi đến phần \"Plugins\" hoặc \"Extensions\""
    echo -e "   - Chọn \"Upload Plugin\" hoặc \"Install Plugin\""
    echo -e "   - Tải lên file ${PLUGIN_NAME}.signed.difypkg"
    echo -e "   - Làm theo hướng dẫn trên màn hình để hoàn tất quá trình cài đặt"
    echo -e "\n${GREEN}Plugin đã sẵn sàng để cài đặt: ${PLUGIN_NAME}.signed.difypkg${NC}"
}

# Hàm main
main() {
    echo -e "${GREEN}=== BẮT ĐẦU ĐÓNG GÓI PLUGIN DIFY ===${NC}"
    echo -e "${YELLOW}Plugin: ${PLUGIN_NAME} v${PLUGIN_VERSION}${NC}\n"
    
    check_os
    download_dify_cli
    generate_key_pair
    package_plugin
    sign_plugin
    verify_signature
    show_installation_guide
    
    echo -e "\n${GREEN}=== HOÀN THÀNH ===${NC}"
}

# Chạy script
main
