# Dify Plugin Standards Compliance Guide

## 📋 Tóm tắt quy chuẩn Dify Plugin

Dựa trên tài liệu ch<PERSON>h thức củ<PERSON>, đ<PERSON><PERSON> là các quy chuẩn cần tuân thủ:

### 1. Manifest.yaml Structure

```yaml
version: "1.0.0"                    # Plugin version (major.minor.patch)
type: "plugin"                      # Plugin type (only "plugin" supported)
author: "aidibiz-team"              # Author (no spaces allowed)
name: "aidibiz_fpt_cloud"           # Plugin name (underscore, no dash)
label:
  en_US: "FPT Cloud AI"
created_at: "2024-06-23T08:00:00Z"  # RFC3339 format
icon: "icon.svg"                    # Icon path
resource:
  memory: 268435456                 # Memory in bytes
  permission:
    model:
      enabled: true
      llm: true                     # LLM permission
      text_embedding: true          # Embedding permission
      # multimodal_llm: NOT SUPPORTED
plugins:
  models:
    - "provider/fpt-cloud.yaml"    # Provider config path
meta:
  version: "0.0.1"                  # Manifest format version
  arch: ["amd64", "arm64"]          # Supported architectures
  runner:
    language: "python"
    version: "3.12"                 # Python 3.12 required
    entrypoint: "main"
```

### 2. Supported Model Types

Dify chỉ hỗ trợ các model types sau:
- ✅ `llm` - Large Language Models
- ✅ `text-embedding` - Text Embedding Models  
- ✅ `rerank` - Rerank Models
- ✅ `speech2text` - Speech to Text
- ✅ `moderation` - Content Moderation
- ✅ `tts` - Text to Speech
- ❌ `multimodal-llm` - KHÔNG được hỗ trợ

### 3. Provider Configuration (provider/fpt-cloud.yaml)

```yaml
provider: fpt-cloud
label:
  en_US: FPT Cloud AI
description:
  en_US: FPT Cloud AI Marketplace models
icon_small:
  en_US: icon_s.svg
icon_large:
  en_US: icon_l.svg
supported_model_types:
  - llm
  - text-embedding
configurate_methods:
  - predefined-model
provider_credential_schema:
  credential_form_schemas:
    - variable: api_key
      label:
        en_US: API Key
      type: secret-input
      required: true
      placeholder:
        en_US: Enter your FPT Cloud API Key
models:
  llm:
    predefined:
      - "models/llm/*.yaml"
  text_embedding:
    predefined:
      - "models/text_embedding/*.yaml"
extra:
  python:
    provider_source: provider/fpt_cloud.py
    model_sources:
      - "models/llm/llm.py"
      - "models/text_embedding/text_embedding.py"
```

### 4. Model Provider Class

```python
from dify_plugin import ModelProvider
from dify_plugin.entities.model import ModelType
from dify_plugin.errors.model import CredentialsValidateFailedError

class FPTCloudProvider(ModelProvider):
    def validate_provider_credentials(self, credentials: dict) -> None:
        """
        Validate provider credentials
        Must implement this method for predefined models
        """
        try:
            model_instance = self.get_model_instance(ModelType.LLM)
            model_instance.validate_credentials(
                model="QwQ-32B", 
                credentials=credentials
            )
        except CredentialsValidateFailedError as ex:
            raise ex
        except Exception as ex:
            logger.exception(f"Provider credentials validation failed")
            raise ex
```

### 5. LLM Model Implementation

```python
from dify_plugin import LargeLanguageModel
from dify_plugin.entities.model import ModelType
from dify_plugin.entities.message import PromptMessage, LLMResult
from typing import Optional, Union, Generator

class FPTCloudLanguageModel(LargeLanguageModel):
    def validate_credentials(self, model: str, credentials: dict) -> None:
        """Validate model credentials"""
        # Implementation here
        
    def _invoke(self, model: str, credentials: dict,
                prompt_messages: list[PromptMessage], 
                model_parameters: dict,
                tools: Optional[list] = None, 
                stop: Optional[list[str]] = None,
                stream: bool = True, 
                user: Optional[str] = None) -> Union[LLMResult, Generator]:
        """Core LLM invocation method"""
        # Implementation here
        
    def get_num_tokens(self, model: str, credentials: dict, 
                       prompt_messages: list[PromptMessage],
                       tools: Optional[list] = None) -> int:
        """Get token count"""
        # Implementation here
        
    @property
    def _invoke_error_mapping(self) -> dict:
        """Map provider errors to Dify errors"""
        return {
            InvokeConnectionError: [ConnectionError],
            InvokeRateLimitError: [RateLimitError],
            InvokeAuthorizationError: [AuthenticationError],
            InvokeBadRequestError: [BadRequestError],
            InvokeServerUnavailableError: [ServerError]
        }
```

### 6. Text Embedding Implementation

```python
from dify_plugin import TextEmbeddingModel
from dify_plugin.entities.model import TextEmbeddingResult

class FPTCloudTextEmbeddingModel(TextEmbeddingModel):
    def validate_credentials(self, model: str, credentials: dict) -> None:
        """Validate embedding model credentials"""
        # Implementation here
        
    def _invoke(self, model: str, credentials: dict,
                texts: list[str], 
                user: Optional[str] = None) -> TextEmbeddingResult:
        """Core embedding invocation method"""
        # Implementation here
        
    def get_num_tokens(self, model: str, credentials: dict, 
                       texts: list[str]) -> int:
        """Get token count for texts"""
        # Implementation here
```

### 7. Main Entry Point

```python
from dify_plugin import Plugin, DifyPluginEnv

plugin = Plugin(DifyPluginEnv(MAX_REQUEST_TIMEOUT=120))

if __name__ == '__main__':
    plugin.run()
```

### 8. Directory Structure

```
aidibiz_fpt_cloud/
├── manifest.yaml                 # Plugin manifest
├── main.py                      # Entry point
├── provider/
│   ├── fpt-cloud.yaml          # Provider config
│   └── fpt_cloud.py            # Provider implementation
├── models/
│   ├── llm/
│   │   ├── llm.py              # LLM implementation
│   │   └── qwq-32b.yaml        # Model config
│   └── text_embedding/
│       ├── text_embedding.py   # Embedding implementation
│       └── vietnamese_embedding.yaml
├── _assets/
│   ├── icon.svg
│   ├── icon_s.svg
│   └── icon_l.svg
└── requirements.txt
```

## 🔧 Các lỗi thường gặp và cách sửa

### 1. Plugin Identifier Error
```
❌ plugin_unique_identifier is not valid: AIDBiz Team/aidibiz-fpt-cloud
✅ Sửa: author: "aidibiz-team", name: "aidibiz_fpt_cloud"
```

### 2. Model Type Validation Error
```
❌ Input should be 'llm', 'text-embedding', 'rerank', 'speech2text', 'moderation' or 'tts'
✅ Sửa: Xóa 'multimodal-llm' khỏi supported_model_types
```

### 3. 504 Gateway Timeout
```
❌ Model validation timeout
✅ Sửa: Implement proper validate_credentials với timeout handling
```

## 📝 Checklist tuân thủ chuẩn

- [ ] Manifest.yaml đúng format và fields bắt buộc
- [ ] Author và name không có dấu cách/ký tự đặc biệt
- [ ] Chỉ sử dụng supported model types
- [ ] Provider class implement validate_provider_credentials
- [ ] Model classes implement validate_credentials
- [ ] Error mapping được định nghĩa đúng
- [ ] Directory structure theo chuẩn
- [ ] Python 3.12 compatibility
- [ ] Proper timeout handling
- [ ] Icon files tồn tại

## 🎯 Kết luận

Plugin AIDiBiz FPT Cloud đã được cập nhật để tuân thủ đầy đủ quy chuẩn Dify:
- Loại bỏ multimodal-llm support
- Sửa plugin identifier format
- Implement đúng interface methods
- Cấu trúc thư mục chuẩn
- Error handling đúng cách
