#!/bin/bash

# Script để setup Dify local để test plugin
set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

DIFY_DIR="dify-test"
PLUGIN_NAME="aidibiz-fpt-cloud"

echo -e "${GREEN}🚀 Setting up Dify for plugin testing${NC}"
echo "=" * 60

# Check if Dock<PERSON> is running
check_docker() {
    echo -e "${YELLOW}Checking Docker...${NC}"
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker is running${NC}"
}

# Clone Dify if not exists
setup_dify() {
    echo -e "${YELLOW}Setting up Dify...${NC}"
    
    if [ ! -d "$DIFY_DIR" ]; then
        echo "Cloning Dify repository..."
        git clone https://github.com/langgenius/dify.git "$DIFY_DIR"
    else
        echo "Dify directory already exists"
    fi
    
    cd "$DIFY_DIR"
    
    # Copy environment files
    if [ ! -f "docker/.env" ]; then
        cp docker/.env.example docker/.env
        echo -e "${GREEN}✅ Created docker/.env${NC}"
    fi
    
    cd ..
}

# Setup plugin files
setup_plugin_files() {
    echo -e "${YELLOW}Setting up plugin files...${NC}"
    
    # Create plugin directory in Dify
    PLUGIN_DIR="$DIFY_DIR/docker/volumes/plugin_daemon"
    mkdir -p "$PLUGIN_DIR/public_keys"
    
    # Copy public key
    if [ -f "aidbiz_key_pair.public.pem" ]; then
        cp aidbiz_key_pair.public.pem "$PLUGIN_DIR/public_keys/"
        echo -e "${GREEN}✅ Copied public key to Dify${NC}"
    else
        echo -e "${RED}❌ Public key not found${NC}"
        exit 1
    fi
    
    # Copy plugin package
    if [ -f "${PLUGIN_NAME}.signed.difypkg" ]; then
        cp "${PLUGIN_NAME}.signed.difypkg" "$PLUGIN_DIR/"
        echo -e "${GREEN}✅ Copied plugin package to Dify${NC}"
    else
        echo -e "${RED}❌ Plugin package not found${NC}"
        exit 1
    fi
}

# Configure Dify for plugins
configure_dify() {
    echo -e "${YELLOW}Configuring Dify for plugins...${NC}"
    
    OVERRIDE_FILE="$DIFY_DIR/docker/docker-compose.override.yaml"
    
    cat > "$OVERRIDE_FILE" << EOF
services:
  plugin_daemon:
    environment:
      FORCE_VERIFYING_SIGNATURE: true
      THIRD_PARTY_SIGNATURE_VERIFICATION_ENABLED: true
      THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS: /app/storage/public_keys/aidbiz_key_pair.public.pem
    volumes:
      - ./volumes/plugin_daemon:/app/storage
EOF
    
    echo -e "${GREEN}✅ Created docker-compose.override.yaml${NC}"
}

# Start Dify
start_dify() {
    echo -e "${YELLOW}Starting Dify...${NC}"
    
    cd "$DIFY_DIR/docker"
    
    # Pull latest images
    echo "Pulling Docker images..."
    docker compose pull
    
    # Start services
    echo "Starting Dify services..."
    docker compose up -d
    
    echo -e "${GREEN}✅ Dify started successfully${NC}"
    echo -e "${YELLOW}Waiting for services to be ready...${NC}"
    sleep 30
    
    cd ../..
}

# Check Dify status
check_dify_status() {
    echo -e "${YELLOW}Checking Dify status...${NC}"
    
    # Check if Dify is accessible
    for i in {1..10}; do
        if curl -s http://localhost > /dev/null; then
            echo -e "${GREEN}✅ Dify is accessible at http://localhost${NC}"
            return 0
        fi
        echo "Waiting for Dify to start... ($i/10)"
        sleep 10
    done
    
    echo -e "${RED}❌ Dify is not accessible after 100 seconds${NC}"
    return 1
}

# Show next steps
show_next_steps() {
    echo -e "\n${GREEN}🎉 Dify setup complete!${NC}"
    echo -e "\n${YELLOW}Next steps:${NC}"
    echo "1. Open Dify in browser: http://localhost"
    echo "2. Complete initial setup (create admin account)"
    echo "3. Go to Settings > Model Providers"
    echo "4. Upload plugin: ${PLUGIN_NAME}.signed.difypkg"
    echo "5. Configure FPT Cloud AI provider with API key"
    echo "6. Test the models:"
    echo "   - LLM: QwQ-32B"
    echo "   - Embedding: Vietnamese_Embedding"
    echo "   - Vision: gemma-3-27b-it"
    echo -e "\n${YELLOW}Useful commands:${NC}"
    echo "- View logs: cd $DIFY_DIR/docker && docker compose logs -f"
    echo "- Stop Dify: cd $DIFY_DIR/docker && docker compose down"
    echo "- Restart Dify: cd $DIFY_DIR/docker && docker compose restart"
}

# Main execution
main() {
    check_docker
    setup_dify
    setup_plugin_files
    configure_dify
    start_dify
    
    if check_dify_status; then
        show_next_steps
    else
        echo -e "${RED}❌ Setup failed. Check Docker logs for details.${NC}"
        echo "Debug command: cd $DIFY_DIR/docker && docker compose logs"
        exit 1
    fi
}

# Run main function
main
