#!/usr/bin/env python3
"""
Test Dify compliance for Cloudflare Workers AI plugin
Check all required functions for model submission
"""
import sys
import os
import inspect

# Add the models directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'models', 'llm'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'models', 'text_embedding'))

def test_required_functions():
    """Test if all required functions are present"""
    print("🔍 Testing Dify Compliance - Required Functions...")
    
    # Required functions for LLM model according to Ollama/LocalAI standards
    required_llm_functions = [
        '_invoke',
        'get_num_tokens', 
        'validate_credentials',
        'get_customizable_model_schema',
        'get_model_mode',
        '_invoke_error_mapping',  # property
        '_generate',
        '_num_tokens_from_messages',
    ]
    
    # Required functions for Text Embedding model
    required_embedding_functions = [
        '_invoke',
        'get_num_tokens',
        'validate_credentials', 
        'get_customizable_model_schema',
        '_invoke_error_mapping',  # property
    ]
    
    try:
        # Test LLM model - check both old and new file locations
        print("\n📋 Testing LLM Model Functions...")
        try:
            # Try new Cloudflare plugin location first
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'cloudflare-workers-ai', 'models', 'llm'))
            from llm import CloudflareWorkersAILanguageModel
            llm_model = CloudflareWorkersAILanguageModel()
            print("✅ Found Cloudflare plugin in cloudflare-workers-ai folder")
        except ImportError:
            try:
                # Try old location
                from cloudflare_workers_ai_llm import CloudflareWorkersAILanguageModel
                llm_model = CloudflareWorkersAILanguageModel()
                print("✅ Found Cloudflare plugin in models folder")
            except ImportError as e:
                print(f"❌ Cannot import CloudflareWorkersAILanguageModel: {e}")
                return False

        missing_llm_functions = []
        for func_name in required_llm_functions:
            if hasattr(llm_model, func_name):
                if func_name == '_invoke_error_mapping':
                    # Check if it's a property
                    if isinstance(getattr(type(llm_model), func_name, None), property):
                        print(f"✅ {func_name} (property)")
                    else:
                        print(f"❌ {func_name} (should be property)")
                        missing_llm_functions.append(f"{func_name} (not property)")
                else:
                    # Check if it's callable
                    if callable(getattr(llm_model, func_name)):
                        print(f"✅ {func_name}")
                    else:
                        print(f"❌ {func_name} (not callable)")
                        missing_llm_functions.append(f"{func_name} (not callable)")
            else:
                print(f"❌ {func_name} (missing)")
                missing_llm_functions.append(func_name)

        # Test Text Embedding model
        print("\n📋 Testing Text Embedding Model Functions...")
        try:
            # Try new Cloudflare plugin location first
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'cloudflare-workers-ai', 'models', 'text_embedding'))
            from text_embedding import CloudflareWorkersAITextEmbeddingModel
            embedding_model = CloudflareWorkersAITextEmbeddingModel()
            print("✅ Found Cloudflare text embedding in cloudflare-workers-ai folder")
        except ImportError:
            try:
                # Try old location
                from cloudflare_workers_ai_text_embedding import CloudflareWorkersAITextEmbeddingModel
                embedding_model = CloudflareWorkersAITextEmbeddingModel()
                print("✅ Found Cloudflare text embedding in models folder")
            except ImportError as e:
                print(f"❌ Cannot import CloudflareWorkersAITextEmbeddingModel: {e}")
                return False
        
        missing_embedding_functions = []
        for func_name in required_embedding_functions:
            if hasattr(embedding_model, func_name):
                if func_name == '_invoke_error_mapping':
                    # Check if it's a property
                    if isinstance(getattr(type(embedding_model), func_name, None), property):
                        print(f"✅ {func_name} (property)")
                    else:
                        print(f"❌ {func_name} (should be property)")
                        missing_embedding_functions.append(f"{func_name} (not property)")
                else:
                    # Check if it's callable
                    if callable(getattr(embedding_model, func_name)):
                        print(f"✅ {func_name}")
                    else:
                        print(f"❌ {func_name} (not callable)")
                        missing_embedding_functions.append(f"{func_name} (not callable)")
            else:
                print(f"❌ {func_name} (missing)")
                missing_embedding_functions.append(func_name)
        
        # Summary
        print("\n" + "="*60)
        print("📊 DIFY COMPLIANCE SUMMARY")
        print("="*60)
        
        if not missing_llm_functions and not missing_embedding_functions:
            print("🎉 All required functions present!")
            print("✅ LLM Model: COMPLIANT")
            print("✅ Text Embedding Model: COMPLIANT")
            return True
        else:
            print("⚠️ Missing required functions:")
            if missing_llm_functions:
                print(f"❌ LLM Model missing: {missing_llm_functions}")
            if missing_embedding_functions:
                print(f"❌ Text Embedding Model missing: {missing_embedding_functions}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import models: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_function_signatures():
    """Test if function signatures match Dify standards"""
    print("\n🔍 Testing Function Signatures...")
    
    try:
        from cloudflare_workers_ai_llm import CloudflareWorkersAILanguageModel
        llm_model = CloudflareWorkersAILanguageModel()
        
        # Test validate_credentials signature
        validate_sig = inspect.signature(llm_model.validate_credentials)
        expected_params = ['model', 'credentials']
        actual_params = list(validate_sig.parameters.keys())[1:]  # Skip 'self'
        
        if actual_params == expected_params:
            print("✅ validate_credentials signature correct")
        else:
            print(f"❌ validate_credentials signature: expected {expected_params}, got {actual_params}")
        
        # Test get_customizable_model_schema signature
        schema_sig = inspect.signature(llm_model.get_customizable_model_schema)
        expected_params = ['model', 'credentials']
        actual_params = list(schema_sig.parameters.keys())[1:]  # Skip 'self'
        
        if actual_params == expected_params:
            print("✅ get_customizable_model_schema signature correct")
        else:
            print(f"❌ get_customizable_model_schema signature: expected {expected_params}, got {actual_params}")
        
        # Test get_model_mode signature
        mode_sig = inspect.signature(llm_model.get_model_mode)
        expected_params = ['model', 'credentials']
        actual_params = list(mode_sig.parameters.keys())[1:]  # Skip 'self'
        
        if actual_params == expected_params:
            print("✅ get_model_mode signature correct")
        else:
            print(f"❌ get_model_mode signature: expected {expected_params}, got {actual_params}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing signatures: {e}")
        return False

def test_error_mapping_structure():
    """Test if _invoke_error_mapping has correct structure"""
    print("\n🔍 Testing Error Mapping Structure...")
    
    try:
        from cloudflare_workers_ai_llm import CloudflareWorkersAILanguageModel
        llm_model = CloudflareWorkersAILanguageModel()
        
        error_mapping = llm_model._invoke_error_mapping
        
        if isinstance(error_mapping, dict):
            print("✅ _invoke_error_mapping is dict")
        else:
            print(f"❌ _invoke_error_mapping should be dict, got {type(error_mapping)}")
            return False
        
        # Check if keys are error types
        required_error_types = [
            'InvokeConnectionError',
            'InvokeServerUnavailableError', 
            'InvokeRateLimitError',
            'InvokeAuthorizationError',
            'InvokeBadRequestError'
        ]
        
        mapping_keys = [key.__name__ for key in error_mapping.keys()]
        
        for error_type in required_error_types:
            if error_type in mapping_keys:
                print(f"✅ {error_type} mapped")
            else:
                print(f"⚠️ {error_type} not mapped (optional)")
        
        print(f"📊 Total error mappings: {len(error_mapping)}")
        return True
        
    except Exception as e:
        print(f"❌ Error testing error mapping: {e}")
        return False

def main():
    """Run all compliance tests"""
    print("🚀 Cloudflare Workers AI Plugin - Dify Compliance Test")
    print("=" * 60)
    print("🎯 Testing compliance with Ollama/LocalAI standards")
    print("=" * 60)
    
    tests = [
        ("Required Functions", test_required_functions),
        ("Function Signatures", test_function_signatures), 
        ("Error Mapping Structure", test_error_mapping_structure),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 FINAL COMPLIANCE SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Plugin is FULLY COMPLIANT with Dify standards!")
        print("✅ Ready for model submission")
    else:
        print("⚠️ Plugin needs fixes to be fully compliant")
    
    print("\n📋 Required Functions Summary:")
    print("✅ validate_credentials - Validates API credentials")
    print("✅ get_customizable_model_schema - Returns model configuration")
    print("✅ get_model_mode - Returns LLM mode (CHAT/COMPLETION)")
    print("✅ _invoke_error_mapping - Maps exceptions to Dify errors")
    print("✅ _invoke - Core model invocation")
    print("✅ get_num_tokens - Token counting")

if __name__ == "__main__":
    main()
