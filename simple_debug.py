#!/usr/bin/env python3
"""
Simple debug script để test plugin AIDiBiz FPT Cloud
Không sử dụng dify_plugin để tránh conflict
"""

import os
import sys
import json
import yaml
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# FPT Cloud configuration
FPT_API_KEY = os.getenv("API_KEY", "")
FPT_BASE_URL = os.getenv("BASE_URL", "https://mkp-api.fptcloud.com")

def check_plugin_files():
    """Check if plugin files exist"""
    print("🔍 Checking plugin files...")
    
    required_files = [
        "manifest.yaml",
        "main.py",
        "provider/fpt-cloud.yaml",
        "provider/fpt_cloud.py",
        "models/llm/llm.py",
        "models/text_embedding/text_embedding.py",
        "aidibiz_fpt_cloud.signed.difypkg"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing plugin files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All plugin files exist")
        return True

def validate_manifest():
    """Validate manifest.yaml"""
    print("🔍 Validating manifest.yaml...")
    
    try:
        with open("manifest.yaml", "r") as f:
            manifest = yaml.safe_load(f)
        
        required_fields = ["name", "version", "type", "plugins"]
        for field in required_fields:
            if field not in manifest:
                print(f"❌ Missing field in manifest.yaml: {field}")
                return False
        
        print(f"✅ Plugin name: {manifest['name']}")
        print(f"✅ Plugin version: {manifest['version']}")
        print(f"✅ Plugin type: {manifest['type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Manifest validation error: {str(e)}")
        return False

def validate_provider_config():
    """Validate provider configuration"""
    print("🔍 Validating provider configuration...")

    try:
        with open("provider/fpt-cloud.yaml", "r") as f:
            config = yaml.safe_load(f)

        required_fields = ["provider", "supported_model_types", "configurate_methods"]
        for field in required_fields:
            if field not in config:
                print(f"❌ Missing field in provider config: {field}")
                return False

        print(f"✅ Provider: {config['provider']}")
        print(f"✅ Supported model types: {config['supported_model_types']}")
        print(f"✅ Configuration methods: {config['configurate_methods']}")

        # Check if has model credential schema for customizable models
        if 'model_credential_schema' in config:
            print(f"✅ Model credential schema available")

        # Check provider credential schema
        if 'provider_credential_schema' in config:
            print(f"✅ Provider credential schema available")

        return True

    except Exception as e:
        print(f"❌ Provider config validation error: {str(e)}")
        return False

def test_fpt_cloud_api():
    """Test FPT Cloud API connection"""
    print("🔍 Testing FPT Cloud API...")
    
    if not FPT_API_KEY:
        print("❌ FPT_API_KEY not found in environment variables")
        return False
    
    try:
        from openai import OpenAI
        
        client = OpenAI(
            api_key=FPT_API_KEY,
            base_url=FPT_BASE_URL
        )
        
        # Test LLM
        print("Testing LLM (QwQ-32B)...")
        llm_response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {"role": "user", "content": "Hello, respond with just 'OK'"}
            ],
            max_tokens=5
        )
        print(f"✅ LLM Response: {llm_response.choices[0].message.content}")
        
        # Test Embedding
        print("Testing Embedding (Vietnamese_Embedding)...")
        embedding_response = client.embeddings.create(
            model="Vietnamese_Embedding",
            input="Test embedding"
        )
        embedding_dim = len(embedding_response.data[0].embedding)
        print(f"✅ Embedding dimension: {embedding_dim}")
        
        return True
        
    except Exception as e:
        print(f"❌ FPT Cloud API error: {str(e)}")
        return False

def check_python_syntax():
    """Check Python syntax of plugin files"""
    print("🔍 Checking Python syntax...")
    
    python_files = [
        "main.py",
        "provider/fpt_cloud.py",
        "models/llm/llm.py",
        "models/text_embedding/text_embedding.py"
    ]
    
    for file_path in python_files:
        try:
            with open(file_path, 'r') as f:
                code = f.read()
            
            # Try to compile the code
            compile(code, file_path, 'exec')
            print(f"✅ {file_path} - syntax OK")
            
        except SyntaxError as e:
            print(f"❌ {file_path} - syntax error: {e}")
            return False
        except Exception as e:
            print(f"❌ {file_path} - error: {e}")
            return False
    
    return True

def check_package_integrity():
    """Check if plugin package exists and is valid"""
    print("🔍 Checking plugin package...")
    
    package_file = "aidibiz_fpt_cloud.signed.difypkg"
    
    if not os.path.exists(package_file):
        print(f"❌ Package file not found: {package_file}")
        return False
    
    file_size = os.path.getsize(package_file)
    print(f"✅ Package file exists: {package_file} ({file_size} bytes)")
    
    # Check if public key exists
    public_key = "aidbiz_key_pair.public.pem"
    if os.path.exists(public_key):
        print(f"✅ Public key exists: {public_key}")
    else:
        print(f"❌ Public key not found: {public_key}")
        return False
    
    return True

def show_deployment_instructions():
    """Show deployment instructions"""
    print("\n🚀 DEPLOYMENT INSTRUCTIONS")
    print("=" * 50)
    print("1. Copy public key to Dify:")
    print("   mkdir -p docker/volumes/plugin_daemon/public_keys")
    print("   cp aidbiz_key_pair.public.pem docker/volumes/plugin_daemon/public_keys/")
    print()
    print("2. Update docker-compose.override.yaml:")
    print("   services:")
    print("     plugin_daemon:")
    print("       environment:")
    print("         FORCE_VERIFYING_SIGNATURE: true")
    print("         THIRD_PARTY_SIGNATURE_VERIFICATION_ENABLED: true")
    print("         THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS: /app/storage/public_keys/aidbiz_key_pair.public.pem")
    print()
    print("3. Restart Dify:")
    print("   cd docker && docker compose down && docker compose up -d")
    print()
    print("4. Upload plugin:")
    print("   - Go to Dify admin panel")
    print("   - Navigate to Plugins section")
    print("   - Upload aidibiz-fpt-cloud.signed.difypkg")
    print()
    print("5. Configure provider:")
    print("   - Go to Model Providers")
    print("   - Find 'FPT Cloud AI'")
    print(f"   - Enter API Key: {FPT_API_KEY[:10]}...")
    print("   - Test with models: QwQ-32B, Vietnamese_Embedding, gemma-3-27b-it")

def main():
    """Main debug function"""
    print("🚀 AIDiBiz FPT Cloud Plugin - Simple Debug")
    print("=" * 50)
    
    tests = [
        ("Plugin Files", check_plugin_files),
        ("Manifest Validation", validate_manifest),
        ("Provider Configuration", validate_provider_config),
        ("Python Syntax", check_python_syntax),
        ("Package Integrity", check_package_integrity),
        ("FPT Cloud API", test_fpt_cloud_api),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DEBUG SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Plugin is ready for deployment.")
        show_deployment_instructions()
    else:
        print("⚠️  Some tests failed. Please fix the issues before deployment.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
