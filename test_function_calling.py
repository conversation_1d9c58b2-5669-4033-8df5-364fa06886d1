#!/usr/bin/env python3
"""
Test script để kiểm tra FPT Cloud API có hỗ trợ function calling không
"""
import os
import json
from openai import OpenAI

# Configuration
API_KEY = "sk-HMHa6NNBNnJdWfl_USuxuQ"
BASE_URL = "https://mkp-api.fptcloud.com"

def test_function_calling_support():
    """Test function calling với các models khác nhau"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    # Define a simple function for testing
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get current weather information for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city name"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    # Test models
    models_to_test = [
        "QwQ-32B",
        "gemma-3-27b-it",
        "Vietnamese_Embedding"  # This should fail anyway as it's embedding
    ]
    
    for model in models_to_test:
        print(f"\n{'='*60}")
        print(f"Testing Function Calling with model: {model}")
        print(f"{'='*60}")
        
        try:
            # Test 1: Without tool_choice (let API decide)
            print(f"\n🧪 Test 1: Function calling without tool_choice")
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "user",
                        "content": "What's the weather like in Hanoi?"
                    }
                ],
                tools=tools,
                # Don't set tool_choice, let API decide
                temperature=0.1
            )
            
            print(f"✅ SUCCESS: {model} supports function calling!")
            print(f"Response: {response.choices[0].message.content}")
            if response.choices[0].message.tool_calls:
                print(f"Tool calls: {response.choices[0].message.tool_calls}")
            
        except Exception as e:
            print(f"❌ FAILED: {model} - {str(e)}")
            
            # Test 2: With tool_choice="none" (force no tools)
            try:
                print(f"\n🧪 Test 2: With tool_choice='none'")
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {
                            "role": "user",
                            "content": "What's the weather like in Hanoi?"
                        }
                    ],
                    tools=tools,
                    tool_choice="none",  # Force no tool usage
                    temperature=0.1
                )
                print(f"✅ SUCCESS with tool_choice='none': {model}")
                print(f"Response: {response.choices[0].message.content}")
                
            except Exception as e2:
                print(f"❌ FAILED even with tool_choice='none': {model} - {str(e2)}")
                
            # Test 3: Without tools at all
            try:
                print(f"\n🧪 Test 3: Without tools (baseline)")
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {
                            "role": "user",
                            "content": "What's the weather like in Hanoi?"
                        }
                    ],
                    # No tools parameter
                    temperature=0.1
                )
                print(f"✅ SUCCESS without tools: {model}")
                print(f"Response: {response.choices[0].message.content[:100]}...")
                
            except Exception as e3:
                print(f"❌ FAILED even without tools: {model} - {str(e3)}")

def test_litellm_direct():
    """Test trực tiếp với LiteLLM như trong tài liệu"""
    print(f"\n{'='*60}")
    print(f"Testing with LiteLLM (as documented)")
    print(f"{'='*60}")
    
    try:
        from litellm import completion
        
        # Test với LiteLLM
        response = completion(
            model="QwQ-32B",
            api_base="https://mkp-api.fptcloud.com",
            api_key=API_KEY,
            messages=[
                {
                    "role": "user",
                    "content": "Hello, how are you?"
                }
            ],
            tools=[
                {
                    "type": "function",
                    "function": {
                        "name": "get_weather",
                        "description": "Get weather",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "location": {"type": "string"}
                            }
                        }
                    }
                }
            ]
        )
        
        print(f"✅ LiteLLM SUCCESS!")
        print(f"Response: {response.choices[0].message.content}")
        
    except Exception as e:
        print(f"❌ LiteLLM FAILED: {str(e)}")
        
    # Test without tools
    try:
        response = completion(
            model="QwQ-32B",
            api_base="https://mkp-api.fptcloud.com",
            api_key=API_KEY,
            messages=[
                {
                    "role": "user",
                    "content": "Hello, how are you?"
                }
            ]
        )
        
        print(f"✅ LiteLLM without tools SUCCESS!")
        print(f"Response: {response.choices[0].message.content[:100]}...")
        
    except Exception as e:
        print(f"❌ LiteLLM without tools FAILED: {str(e)}")

if __name__ == "__main__":
    print("🚀 FPT Cloud API Function Calling Test")
    print("=" * 60)
    
    # Test với OpenAI client
    test_function_calling_support()
    
    # Test với LiteLLM
    test_litellm_direct()
    
    print(f"\n{'='*60}")
    print("🎯 CONCLUSION:")
    print("- FPT Cloud API đang sử dụng LiteLLM (confirmed từ documentation)")
    print("- Cần test để xác định function calling support")
    print("- Nếu tất cả đều fail với tools → FPT Cloud chưa enable function calling")
    print("=" * 60)
