import logging
from dify_plugin import <PERSON><PERSON>rovider
from dify_plugin.entities.model import ModelType
from dify_plugin.errors.model import CredentialsValidateFailedError

logger = logging.getLogger(__name__)


class CloudflareWorkersAIProvider(ModelProvider):
    def validate_provider_credentials(self, credentials: dict) -> None:
        """
        Validate provider credentials

        Provider credentials are optional for Cloudflare Workers AI.
        Each model will have its own API token and account ID.

        :param credentials: provider credentials, credentials form defined in `provider_credential_schema`.
        """
        # Provider credentials are optional - validation happens at model level
        # Just check if default_account_id is provided and is a string
        default_account_id = credentials.get("default_account_id")
        if default_account_id and not isinstance(default_account_id, str):
            raise CredentialsValidateFailedError("Default Account ID must be a string")

        logger.info("Provider credentials validated successfully")
