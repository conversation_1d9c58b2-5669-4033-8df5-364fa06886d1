background: '#F38020'
configurate_methods:
- customizable-model
extra:
  python:
    model_sources:
    - models/llm/cloudflare_workers_ai_llm.py
    - models/text_embedding/cloudflare_workers_ai_text_embedding.py
    provider_source: provider/cloudflare_workers_ai.py
help:
  title:
    en_US: How to get your Cloudflare Workers AI API token
  url:
    en_US: https://developers.cloudflare.com/workers-ai/get-started/rest-api/
icon_large:
  en_US: icon_l_en.svg
icon_small:
  en_US: icon_s_en.svg
label:
  en_US: Cloudflare Workers AI
model_credential_schema:
  credential_form_schemas:
  - label:
      en_US: API Token
    placeholder:
      en_US: Enter your Cloudflare Workers AI API Token for this model
    required: true
    type: secret-input
    variable: api_token
  - label:
      en_US: Account ID
    placeholder:
      en_US: Enter your Cloudflare Account ID
    required: true
    type: text-input
    variable: account_id
  - label:
      en_US: Context Size
    placeholder:
      en_US: Enter context size (e.g., 32768)
    required: true
    type: text-input
    variable: context_size
  - label:
      en_US: Max Tokens
    placeholder:
      en_US: Enter max tokens (e.g., 4096)
    required: true
    type: text-input
    variable: max_tokens
  - default: 'false'
    label:
      en_US: Vision Support
    options:
    - label:
        en_US: 'Yes'
      value: 'true'
    - label:
        en_US: 'No'
      value: 'false'
    required: true
    show_on:
    - variable: __model_type
      value: llm
    type: radio
    variable: vision_support
  - default: 'false'
    label:
      en_US: Function Call Support
    options:
    - label:
        en_US: 'Yes'
      value: 'true'
    - label:
        en_US: 'No'
      value: 'false'
    required: true
    show_on:
    - variable: __model_type
      value: llm
    type: radio
    variable: function_call_support
  model:
    label:
      en_US: Model Name
    placeholder:
      en_US: "Enter your model name (e.g., @cf/qwen/qwq-32b-preview)"


provider: cloudflare-workers-ai
provider_credential_schema:
  credential_form_schemas:
  - label:
      en_US: Default Account ID
    placeholder:
      en_US: Enter your default Cloudflare Account ID (can be overridden per model)
    required: false
    type: text-input
    variable: default_account_id
supported_model_types:
- llm
- text-embedding
