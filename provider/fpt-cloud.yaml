background: '#FF6B35'
configurate_methods:
- customizable-model
extra:
  python:
    model_sources:
      - models/llm/llm.py
      - models/text_embedding/text_embedding.py
    provider_source: provider/fpt_cloud.py
help:
  title:
    en_US: How to get your FPT Cloud AI Marketplace API key
    vi_VN: Cách lấy API key FPT Cloud AI Marketplace
  url:
    en_US: https://marketplace.fptcloud.com/en/my-account#my-api-key
    vi_VN: https://marketplace.fptcloud.com/vi/my-account#my-api-key
icon_large:
  en_US: icon_l_en.svg
icon_small:
  en_US: icon_s_en.svg
label:
  en_US: FPT Cloud AI
  vi_VN: FPT Cloud AI
model_credential_schema:
  credential_form_schemas:
  - label:
      en_US: Server URL
      vi_VN: URL máy chủ
    placeholder:
      en_US: Enter FPT Cloud API base URL, e.g. https://mkp-api.fptcloud.com
      vi_VN: Nhập URL API FPT Cloud, ví dụ https://mkp-api.fptcloud.com
    required: true
    type: text-input
    variable: server_url
  - label:
      en_US: API Key
      vi_VN: Khóa API
    placeholder:
      en_US: Enter your FPT Cloud AI Marketplace API Key
      vi_VN: Nhập khóa API FPT Cloud AI Marketplace của bạn
    required: true
    type: secret-input
    variable: api_key
  - label:
      en_US: Context Size
      vi_VN: Kích thước ngữ cảnh
    placeholder:
      en_US: "Enter context size (e.g., 4096, 8192, 32768)"
      vi_VN: "Nhập kích thước ngữ cảnh (ví dụ: 4096, 8192, 32768)"
    required: false
    type: text-input
    variable: context_size
    show_on:
    - value: llm
      variable: __model_type
  - label:
      en_US: Max Tokens (Upper Bound)
      vi_VN: Số token tối đa (Giới hạn trên)
    placeholder:
      en_US: "Enter max tokens upper bound (e.g., 1024, 2048, 4096)"
      vi_VN: "Nhập giới hạn trên số token (ví dụ: 1024, 2048, 4096)"
    required: false
    type: text-input
    variable: max_tokens
    show_on:
    - value: llm
      variable: __model_type
  - default: 'false'
    label:
      en_US: Vision support
      vi_VN: Hỗ trợ Vision
    options:
    - label:
        en_US: 'Yes'
        vi_VN: 'Có'
      value: 'true'
    - label:
        en_US: 'No'
        vi_VN: 'Không'
      value: 'false'
    required: false
    show_on:
    - value: llm
      variable: __model_type
    type: radio
    variable: vision_support
  - default: 'false'
    label:
      en_US: Function call support (Not Available)
      vi_VN: Hỗ trợ Function Call (Không khả dụng)
    options:
    - label:
        en_US: 'No (Server limitation)'
        vi_VN: 'Không (Giới hạn server)'
      value: 'false'
    required: false
    show_on:
    - value: llm
      variable: __model_type
    type: radio
    variable: function_call_support
  model:
    label:
      en_US: Model Name
      vi_VN: Tên mô hình
    placeholder:
      en_US: "Enter your model name (e.g., QwQ-32B, Vietnamese_Embedding)"
      vi_VN: "Nhập tên mô hình của bạn (ví dụ: QwQ-32B, Vietnamese_Embedding)"


provider: fpt-cloud
provider_credential_schema:
  credential_form_schemas:
  - label:
      en_US: API Key
      vi_VN: Khóa API
    placeholder:
      en_US: Enter your FPT Cloud AI Marketplace API Key
      vi_VN: Nhập khóa API FPT Cloud AI Marketplace của bạn
    required: true
    type: secret-input
    variable: api_key
supported_model_types:
- llm
- text-embedding
