#!/bin/bash

# Setup debug environment for AIDiBiz FPT Cloud Plugin
set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🚀 Setting up debug environment for AIDiBiz FPT Cloud Plugin${NC}"
echo "=" * 60

# Check if Python 3 is installed
check_python() {
    echo -e "${YELLOW}Checking Python installation...${NC}"
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        echo -e "${GREEN}✅ Python found: $PYTHON_VERSION${NC}"
    else
        echo -e "${RED}❌ Python 3 not found. Please install Python 3.8+${NC}"
        exit 1
    fi
}

# Install required packages
install_dependencies() {
    echo -e "${YELLOW}Installing dependencies...${NC}"
    
    # Check if requirements.txt exists
    if [ -f "requirements.txt" ]; then
        echo "Installing from requirements.txt..."
        pip3 install -r requirements.txt
    else
        echo "Installing basic dependencies..."
        pip3 install openai python-dotenv requests pyyaml
    fi
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Setup environment
setup_environment() {
    echo -e "${YELLOW}Setting up environment...${NC}"
    
    # Copy debug env if .env doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.debug" ]; then
            cp .env.debug .env
            echo -e "${GREEN}✅ Created .env from .env.debug${NC}"
        else
            echo -e "${RED}❌ No .env.debug file found${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ .env file already exists${NC}"
    fi
}

# Check plugin files
check_plugin_files() {
    echo -e "${YELLOW}Checking plugin files...${NC}"
    
    REQUIRED_FILES=(
        "manifest.yaml"
        "main.py"
        "provider/fpt-cloud.yaml"
        "provider/fpt_cloud.py"
        "models/llm/llm.py"
        "models/text_embedding/text_embedding.py"
        "models/multimodal_llm/multimodal_llm.py"
    )
    
    MISSING_FILES=()
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            MISSING_FILES+=("$file")
        fi
    done
    
    if [ ${#MISSING_FILES[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ All required plugin files exist${NC}"
    else
        echo -e "${RED}❌ Missing plugin files:${NC}"
        for file in "${MISSING_FILES[@]}"; do
            echo -e "${RED}   - $file${NC}"
        done
        exit 1
    fi
}

# Run debug tests
run_debug_tests() {
    echo -e "${YELLOW}Running debug tests...${NC}"
    
    if [ -f "debug_plugin.py" ]; then
        python3 debug_plugin.py
    else
        echo -e "${RED}❌ debug_plugin.py not found${NC}"
        exit 1
    fi
}

# Show next steps
show_next_steps() {
    echo -e "\n${GREEN}🎉 Debug environment setup complete!${NC}"
    echo -e "\n${YELLOW}Next steps:${NC}"
    echo "1. Update .env file with your Dify API credentials if needed"
    echo "2. Run debug tests: python3 debug_plugin.py"
    echo "3. Install plugin to Dify: Upload aidibiz-fpt-cloud.signed.difypkg"
    echo "4. Test plugin in Dify interface"
    echo -e "\n${YELLOW}Debug commands:${NC}"
    echo "- Test FPT Cloud API: python3 test_client.py"
    echo "- Debug plugin: python3 debug_plugin.py"
    echo "- Package plugin: ./package_plugin.sh"
}

# Main execution
main() {
    check_python
    install_dependencies
    setup_environment
    check_plugin_files
    run_debug_tests
    show_next_steps
}

# Run main function
main
