background: '#F3F4F6'
configurate_methods:
- customizable-model
extra:
  python:
    model_sources:
    - models/llm/llm.py
    - models/rerank/rerank.py
    - models/text_embedding/text_embedding.py
    - models/speech2text/speech2text.py
    provider_source: provider/localai.py
help:
  title:
    en_US: How to deploy LocalAI
    zh_Hans: 如何部署 LocalAI
  url:
    en_US: https://github.com/go-skynet/LocalAI
icon_large:
  en_US: icon_l_en.svg
icon_small:
  en_US: icon_s_en.svg
label:
  en_US: LocalAI
model_credential_schema:
  credential_form_schemas:
  - default: chat_completion
    label:
      en_US: Completion type
    options:
    - label:
        en_US: Completion
        zh_Hans: 补全
      value: completion
    - label:
        en_US: ChatCompletion
        zh_Hans: 对话
      value: chat_completion
    placeholder:
      en_US: Select completion type
      zh_Hans: 选择对话类型
    required: false
    show_on:
    - value: llm
      variable: __model_type
    type: select
    variable: completion_type
  - label:
      en_US: Server url
      zh_Hans: 服务器URL
    placeholder:
      en_US: Enter the url of your LocalAI, e.g. http://192.168.1.100:8080
      zh_Hans: 在此输入LocalAI的服务器地址，如 http://192.168.1.100:8080
    required: true
    type: text-input
    variable: server_url
  - label:
      en_US: Context size
      zh_Hans: 上下文大小
    placeholder:
      en_US: Enter context size
      zh_Hans: 输入上下文大小
    required: false
    show_on:
    - value: llm
      variable: __model_type
    type: text-input
    variable: context_size
  model:
    label:
      en_US: Model Name
      zh_Hans: 模型名称
    placeholder:
      en_US: Enter your model name
      zh_Hans: 输入模型名称
provider: localai
supported_model_types:
- llm
- text-embedding
- rerank
- speech2text
