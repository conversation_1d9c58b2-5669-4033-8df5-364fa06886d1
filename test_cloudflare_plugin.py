#!/usr/bin/env python3
"""
Comprehensive test script for Cloudflare Workers AI Plugin
"""

import os
import sys
import json
import yaml
import zipfile
import tempfile
import shutil
from pathlib import Path

def test_plugin_structure():
    """Test plugin structure and files"""
    print("🔍 Testing Plugin Structure...")
    
    required_files = [
        "manifest.yaml",
        "main.py",
        "provider/cloudflare-workers-ai.yaml",
        "provider/cloudflare_workers_ai.py",
        "models/llm/cloudflare_workers_ai_llm.py",
        "models/text_embedding/cloudflare_workers_ai_text_embedding.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All required files present")
        return True

def test_manifest_yaml():
    """Test manifest.yaml structure"""
    print("🔍 Testing manifest.yaml...")
    
    try:
        with open("manifest.yaml", "r") as f:
            manifest = yaml.safe_load(f)
        
        required_fields = ["name", "version", "author", "type", "plugins"]
        missing_fields = [field for field in required_fields if field not in manifest]
        
        if missing_fields:
            print(f"❌ Missing manifest fields: {missing_fields}")
            return False
        
        if manifest["name"] != "aidibiz_cloudflare_workers_ai":
            print(f"❌ Incorrect plugin name: {manifest['name']}")
            return False
        
        if manifest["type"] != "plugin":
            print(f"❌ Incorrect plugin type: {manifest['type']}")
            return False
        
        print("✅ manifest.yaml is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error reading manifest.yaml: {e}")
        return False

def test_provider_yaml():
    """Test provider YAML configuration"""
    print("🔍 Testing provider configuration...")
    
    try:
        with open("provider/cloudflare-workers-ai.yaml", "r") as f:
            provider_config = yaml.safe_load(f)
        
        required_fields = ["provider", "supported_model_types", "configurate_methods"]
        missing_fields = [field for field in required_fields if field not in provider_config]
        
        if missing_fields:
            print(f"❌ Missing provider fields: {missing_fields}")
            return False
        
        if provider_config["provider"] != "cloudflare-workers-ai":
            print(f"❌ Incorrect provider name: {provider_config['provider']}")
            return False
        
        if "customizable-model" not in provider_config["configurate_methods"]:
            print("❌ Missing customizable-model configuration method")
            return False
        
        supported_types = provider_config["supported_model_types"]
        if "llm" not in supported_types or "text-embedding" not in supported_types:
            print(f"❌ Missing required model types: {supported_types}")
            return False
        
        print("✅ Provider configuration is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error reading provider configuration: {e}")
        return False

def test_python_syntax():
    """Test Python files for syntax errors"""
    print("🔍 Testing Python syntax...")
    
    python_files = [
        "main.py",
        "provider/cloudflare_workers_ai.py",
        "models/llm/cloudflare_workers_ai_llm.py",
        "models/text_embedding/cloudflare_workers_ai_text_embedding.py"
    ]
    
    for file_path in python_files:
        try:
            with open(file_path, "r") as f:
                compile(f.read(), file_path, "exec")
            print(f"✅ {file_path} - syntax OK")
        except SyntaxError as e:
            print(f"❌ {file_path} - syntax error: {e}")
            return False
        except Exception as e:
            print(f"❌ {file_path} - error: {e}")
            return False
    
    return True

def test_plugin_package():
    """Test plugin package integrity"""
    print("🔍 Testing plugin package...")
    
    package_file = "aidibiz_cloudflare_workers_ai.signed.difypkg"
    
    if not os.path.exists(package_file):
        print(f"❌ Plugin package not found: {package_file}")
        return False
    
    try:
        # Test if it's a valid zip file
        with zipfile.ZipFile(package_file, 'r') as zip_file:
            file_list = zip_file.namelist()
            
            # Check for essential files in the package
            essential_files = ["manifest.yaml", "main.py"]
            missing_in_package = [f for f in essential_files if f not in file_list]
            
            if missing_in_package:
                print(f"❌ Missing files in package: {missing_in_package}")
                return False
            
            print(f"✅ Plugin package is valid ({len(file_list)} files)")
            return True
            
    except Exception as e:
        print(f"❌ Error reading plugin package: {e}")
        return False

def test_api_token():
    """Test Cloudflare API token"""
    print("🔍 Testing Cloudflare API token...")
    
    import httpx
    
    api_token = "jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd"
    
    headers = {
        "Authorization": f"Bearer {api_token}",
        "Content-Type": "application/json"
    }
    
    try:
        with httpx.Client() as client:
            response = client.get(
                "https://api.cloudflare.com/client/v4/user/tokens/verify",
                headers=headers,
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print("✅ API Token is valid and active")
                    return True
                else:
                    print("❌ API Token validation failed")
                    return False
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing API token: {e}")
        return False

def run_all_tests():
    """Run all tests and provide summary"""
    print("🧪 Cloudflare Workers AI Plugin Test Suite")
    print("=" * 60)
    
    tests = [
        ("Plugin Structure", test_plugin_structure),
        ("Manifest YAML", test_manifest_yaml),
        ("Provider Configuration", test_provider_yaml),
        ("Python Syntax", test_python_syntax),
        ("Plugin Package", test_plugin_package),
        ("API Token", test_api_token),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print("-" * 60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Plugin is ready for deployment.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix issues before deployment.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
