#!/usr/bin/env python3
"""
Test script for updated Cloudflare Workers AI Plugin with per-model token configuration
"""

import yaml
import json

def test_updated_configuration():
    """Test the updated plugin configuration"""
    print("🔍 Testing Updated Plugin Configuration...")
    
    try:
        with open("provider/cloudflare-workers-ai.yaml", "r") as f:
            config = yaml.safe_load(f)
        
        print("✅ YAML file loads successfully")
        
        # Test model credential schema
        model_creds = config["model_credential_schema"]["credential_form_schemas"]
        model_variables = [cred.get("variable") for cred in model_creds if "variable" in cred]
        
        expected_model_vars = ["api_token", "account_id", "context_size", "max_tokens", "vision_support", "function_call_support"]
        
        print(f"📋 Model credential variables: {model_variables}")
        
        for var in expected_model_vars:
            if var in model_variables:
                print(f"  ✅ {var} - present")
            else:
                print(f"  ❌ {var} - missing")
                return False
        
        # Test provider credential schema
        provider_creds = config["provider_credential_schema"]["credential_form_schemas"]
        provider_variables = [cred.get("variable") for cred in provider_creds if "variable" in cred]
        
        print(f"📋 Provider credential variables: {provider_variables}")
        
        if "default_account_id" in provider_variables:
            print("  ✅ default_account_id - present")
        else:
            print("  ❌ default_account_id - missing")
            return False
        
        # Check that API token is in model credentials, not provider
        api_token_in_model = "api_token" in model_variables
        api_token_in_provider = "api_token" in provider_variables
        
        if api_token_in_model and not api_token_in_provider:
            print("  ✅ API token correctly placed in model credentials")
        else:
            print("  ❌ API token placement incorrect")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

def test_credential_flow():
    """Test the credential flow logic"""
    print("\n🔍 Testing Credential Flow Logic...")
    
    # Simulate different credential scenarios
    scenarios = [
        {
            "name": "Complete model credentials",
            "credentials": {
                "api_token": "test-token",
                "account_id": "test-account",
                "context_size": "32768",
                "max_tokens": "4096"
            },
            "expected": "success"
        },
        {
            "name": "Model with fallback account ID",
            "credentials": {
                "api_token": "test-token",
                "default_account_id": "fallback-account",
                "context_size": "32768",
                "max_tokens": "4096"
            },
            "expected": "success"
        },
        {
            "name": "Missing API token",
            "credentials": {
                "account_id": "test-account",
                "context_size": "32768",
                "max_tokens": "4096"
            },
            "expected": "fail"
        },
        {
            "name": "Missing account ID",
            "credentials": {
                "api_token": "test-token",
                "context_size": "32768",
                "max_tokens": "4096"
            },
            "expected": "fail"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 Testing: {scenario['name']}")
        
        creds = scenario["credentials"]
        
        # Simulate credential validation logic
        api_token = creds.get("api_token")
        account_id = creds.get("account_id")
        
        # Fallback to provider credentials if not set in model
        if not account_id:
            account_id = creds.get("default_account_id")
        
        if not api_token:
            result = "fail - API token missing"
        elif not account_id:
            result = "fail - Account ID missing"
        else:
            result = "success"
        
        expected = scenario["expected"]
        if (result == "success" and expected == "success") or (result.startswith("fail") and expected == "fail"):
            print(f"  ✅ {result}")
        else:
            print(f"  ❌ Expected {expected}, got {result}")
            return False
    
    return True

def show_configuration_example():
    """Show example configuration for users"""
    print("\n📋 Configuration Example for Users:")
    print("=" * 60)
    
    print("\n🔧 Provider Configuration (Optional):")
    provider_config = {
        "default_account_id": "your-default-cloudflare-account-id"
    }
    print(json.dumps(provider_config, indent=2))
    
    print("\n🔧 Model Configuration (Required for each model):")
    model_config = {
        "api_token": "your-cloudflare-api-token-for-this-model",
        "account_id": "account-id-for-this-model-or-use-default",
        "model": "@cf/qwen/qwq-32b-preview",
        "context_size": "32768",
        "max_tokens": "4096",
        "vision_support": "false",
        "function_call_support": "false"
    }
    print(json.dumps(model_config, indent=2))
    
    print("\n📝 Notes:")
    print("- Each model needs its own API token")
    print("- Account ID can be set per model or use default from provider")
    print("- This allows different models to use different Cloudflare accounts/tokens")
    print("- More secure as tokens are scoped per model")

def run_updated_tests():
    """Run all updated tests"""
    print("🧪 Updated Cloudflare Workers AI Plugin Tests")
    print("=" * 60)
    
    tests = [
        ("Updated Configuration", test_updated_configuration),
        ("Credential Flow Logic", test_credential_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 UPDATED TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<40} {status}")
    
    print("-" * 60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    
    if passed == total:
        print("\n🎉 ALL UPDATED TESTS PASSED!")
        print("Plugin now supports per-model token configuration!")
        show_configuration_example()
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        return False

if __name__ == "__main__":
    success = run_updated_tests()
    exit(0 if success else 1)
