#!/usr/bin/env python3
"""
Debug script để kiểm tra model validation và timeout issues
"""

import os
import sys
import time
import requests
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables
load_dotenv()

API_KEY = os.getenv("API_KEY", "sk-HMHa6NNBNnJdWfl_USuxuQ")
BASE_URL = os.getenv("BASE_URL", "https://mkp-api.fptcloud.com")

def test_api_connectivity():
    """Test basic API connectivity"""
    print("🔍 Testing API connectivity...")
    
    try:
        # Test basic HTTP connection
        response = requests.get(BASE_URL, timeout=10)
        print(f"✅ Base URL accessible: {response.status_code}")
        return True
    except requests.exceptions.Timeout:
        print("❌ Connection timeout to base URL")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_model_validation_simple():
    """Test simple model validation"""
    print("🔍 Testing simple model validation...")
    
    try:
        client = OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL
        )
        
        start_time = time.time()
        
        # Very simple request with minimal tokens
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {"role": "user", "content": "Hi"}
            ],
            max_tokens=1,
            timeout=30  # 30 second timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ Simple validation successful")
        print(f"   Response time: {duration:.2f} seconds")
        print(f"   Response: {response.choices[0].message.content}")
        
        return True, duration
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ Simple validation failed after {duration:.2f} seconds")
        print(f"   Error: {e}")
        return False, duration

def test_model_validation_detailed():
    """Test detailed model validation like Dify might do"""
    print("🔍 Testing detailed model validation...")
    
    try:
        client = OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL
        )
        
        # Test different scenarios that Dify might test
        test_cases = [
            {
                "name": "Basic chat",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 5
            },
            {
                "name": "System message",
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Hi"}
                ],
                "max_tokens": 5
            },
            {
                "name": "Temperature test",
                "messages": [{"role": "user", "content": "Hi"}],
                "max_tokens": 5,
                "temperature": 0.7
            },
            {
                "name": "Top-p test",
                "messages": [{"role": "user", "content": "Hi"}],
                "max_tokens": 5,
                "top_p": 0.9
            }
        ]
        
        for test_case in test_cases:
            print(f"   Testing: {test_case['name']}")
            start_time = time.time()
            
            try:
                response = client.chat.completions.create(
                    model="QwQ-32B",
                    timeout=30,
                    **{k: v for k, v in test_case.items() if k != 'name'}
                )
                
                duration = time.time() - start_time
                print(f"   ✅ {test_case['name']}: {duration:.2f}s")
                
            except Exception as e:
                duration = time.time() - start_time
                print(f"   ❌ {test_case['name']}: {duration:.2f}s - {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Detailed validation failed: {e}")
        return False

def test_embedding_validation():
    """Test embedding model validation"""
    print("🔍 Testing embedding model validation...")
    
    try:
        client = OpenAI(
            api_key=API_KEY,
            base_url=BASE_URL
        )
        
        start_time = time.time()
        
        response = client.embeddings.create(
            model="Vietnamese_Embedding",
            input="Test text",
            timeout=30
        )
        
        duration = time.time() - start_time
        
        print(f"✅ Embedding validation successful")
        print(f"   Response time: {duration:.2f} seconds")
        print(f"   Dimension: {len(response.data[0].embedding)}")
        
        return True, duration
        
    except Exception as e:
        duration = time.time() - start_time
        print(f"❌ Embedding validation failed after {duration:.2f} seconds")
        print(f"   Error: {e}")
        return False, duration

def test_concurrent_requests():
    """Test concurrent requests to see if there are rate limits"""
    print("🔍 Testing concurrent requests...")
    
    import threading
    import queue
    
    results = queue.Queue()
    
    def make_request(request_id):
        try:
            client = OpenAI(
                api_key=API_KEY,
                base_url=BASE_URL
            )
            
            start_time = time.time()
            response = client.chat.completions.create(
                model="QwQ-32B",
                messages=[{"role": "user", "content": f"Request {request_id}"}],
                max_tokens=1,
                timeout=30
            )
            duration = time.time() - start_time
            
            results.put(("success", request_id, duration))
            
        except Exception as e:
            duration = time.time() - start_time
            results.put(("error", request_id, duration, str(e)))
    
    # Start 3 concurrent requests
    threads = []
    for i in range(3):
        thread = threading.Thread(target=make_request, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads
    for thread in threads:
        thread.join()
    
    # Collect results
    success_count = 0
    total_time = 0
    
    while not results.empty():
        result = results.get()
        if result[0] == "success":
            success_count += 1
            total_time += result[2]
            print(f"   ✅ Request {result[1]}: {result[2]:.2f}s")
        else:
            print(f"   ❌ Request {result[1]}: {result[2]:.2f}s - {result[3]}")
    
    if success_count > 0:
        avg_time = total_time / success_count
        print(f"✅ Concurrent test: {success_count}/3 successful, avg time: {avg_time:.2f}s")
        return True
    else:
        print("❌ All concurrent requests failed")
        return False

def check_dify_timeout_settings():
    """Provide recommendations for Dify timeout settings"""
    print("🔍 Dify timeout recommendations...")
    
    print("📋 Common timeout issues and solutions:")
    print("1. Model validation timeout (30s default)")
    print("   - Increase timeout in Dify settings")
    print("   - Check if model responds within 30s")
    print()
    print("2. Network connectivity issues")
    print("   - Check firewall settings")
    print("   - Verify DNS resolution")
    print()
    print("3. API rate limiting")
    print("   - Check FPT Cloud rate limits")
    print("   - Implement retry logic")
    print()
    print("4. Dify configuration")
    print("   - Check plugin daemon settings")
    print("   - Verify model provider configuration")

def main():
    """Main debug function"""
    print("🚀 Debug Model Validation - 504 Gateway Timeout")
    print("=" * 60)
    print(f"API Key: {API_KEY[:20]}...")
    print(f"Base URL: {BASE_URL}")
    print()
    
    tests = [
        ("API Connectivity", test_api_connectivity),
        ("Simple Model Validation", lambda: test_model_validation_simple()[0]),
        ("Detailed Model Validation", test_model_validation_detailed),
        ("Embedding Validation", lambda: test_embedding_validation()[0]),
        ("Concurrent Requests", test_concurrent_requests),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        results[test_name] = test_func()
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEBUG SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed < total:
        print("\n⚠️  Issues detected:")
        check_dify_timeout_settings()
        
        print("\n🔧 Recommended actions:")
        print("1. Check Dify logs: docker logs dify-api")
        print("2. Increase timeout in Dify model provider settings")
        print("3. Test API directly outside of Dify")
        print("4. Check network connectivity between Dify and FPT Cloud")
    else:
        print("\n🎉 All tests passed!")
        print("The issue might be specific to Dify's validation process.")
        print("Try adding the model with a longer timeout setting.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
