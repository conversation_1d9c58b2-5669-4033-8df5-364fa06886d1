#!/usr/bin/env python3
"""
Script để test kết n<PERSON>i đến remote Dify server
"""

import os
import sys
import requests
import socket
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

REMOTE_HOST = os.getenv("REMOTE_INSTALL_HOST", "localhost")
REMOTE_PORT = int(os.getenv("REMOTE_INSTALL_PORT", "5003"))
REMOTE_KEY = os.getenv("REMOTE_INSTALL_KEY", "")

def test_network_connectivity():
    """Test basic network connectivity"""
    print(f"🔍 Testing network connectivity to {REMOTE_HOST}:{REMOTE_PORT}")
    
    try:
        # Test TCP connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((REMOTE_HOST, REMOTE_PORT))
        sock.close()
        
        if result == 0:
            print("✅ TCP connection successful")
            return True
        else:
            print(f"❌ TCP connection failed (error code: {result})")
            return False
            
    except socket.gaierror as e:
        print(f"❌ DNS resolution failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Network test error: {e}")
        return False

def test_http_connectivity():
    """Test HTTP connectivity"""
    print(f"🔍 Testing HTTP connectivity")
    
    try:
        # Test basic HTTP connection
        url = f"http://{REMOTE_HOST}:{REMOTE_PORT}"
        response = requests.get(url, timeout=10)
        
        print(f"✅ HTTP response: {response.status_code}")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ HTTP connection failed")
        return False
    except requests.exceptions.Timeout:
        print("❌ HTTP request timeout")
        return False
    except Exception as e:
        print(f"❌ HTTP test error: {e}")
        return False

def test_dify_api():
    """Test Dify API endpoints"""
    print(f"🔍 Testing Dify API")
    
    if not REMOTE_KEY:
        print("❌ REMOTE_INSTALL_KEY not found in .env")
        return False
    
    headers = {
        "Authorization": f"Bearer {REMOTE_KEY}",
        "Content-Type": "application/json"
    }
    
    # Test different endpoints
    endpoints = [
        "/v1/plugins",
        "/v1/workspaces/current/model-providers",
        "/console/api/plugins"
    ]
    
    for endpoint in endpoints:
        try:
            url = f"http://{REMOTE_HOST}:{REMOTE_PORT}{endpoint}"
            response = requests.get(url, headers=headers, timeout=10)
            
            print(f"✅ {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                return True
                
        except Exception as e:
            print(f"❌ {endpoint}: {str(e)}")
    
    return False

def test_alternative_ports():
    """Test common Dify ports"""
    print(f"🔍 Testing alternative ports on {REMOTE_HOST}")
    
    common_ports = [80, 443, 3000, 5000, 8000, 8080]
    
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((REMOTE_HOST, port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port} is open")
                
                # Test HTTP on this port
                try:
                    url = f"http://{REMOTE_HOST}:{port}"
                    response = requests.get(url, timeout=5)
                    print(f"   HTTP response: {response.status_code}")
                except:
                    pass
            else:
                print(f"❌ Port {port} is closed")
                
        except Exception as e:
            print(f"❌ Port {port} test error: {e}")

def show_troubleshooting():
    """Show troubleshooting steps"""
    print("\n🔧 TROUBLESHOOTING STEPS")
    print("=" * 40)
    print("1. Check if Dify server is running:")
    print(f"   ssh user@{REMOTE_HOST}")
    print("   docker ps | grep dify")
    print()
    print("2. Check Dify configuration:")
    print("   - Ensure plugin daemon is enabled")
    print("   - Check port binding (should bind to 0.0.0.0, not 127.0.0.1)")
    print()
    print("3. Check firewall:")
    print(f"   - Port {REMOTE_PORT} should be open")
    print("   - Check iptables/ufw rules")
    print()
    print("4. Check Dify logs:")
    print("   docker logs dify-plugin-daemon")
    print()
    print("5. Alternative installation methods:")
    print("   - Use Dify web interface")
    print("   - Copy plugin file directly to server")
    print("   - Use SSH/SCP to transfer files")

def main():
    """Main function"""
    print("🚀 Testing Remote Dify Connection")
    print("=" * 40)
    print(f"Host: {REMOTE_HOST}")
    print(f"Port: {REMOTE_PORT}")
    print(f"Key: {REMOTE_KEY[:20]}..." if REMOTE_KEY else "No key")
    print()
    
    tests = [
        ("Network Connectivity", test_network_connectivity),
        ("HTTP Connectivity", test_http_connectivity),
        ("Dify API", test_dify_api),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        results[test_name] = test_func()
    
    # Test alternative ports if main connection failed
    if not any(results.values()):
        test_alternative_ports()
    
    # Summary
    print("\n📊 CONNECTION TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == 0:
        show_troubleshooting()
    elif passed < total:
        print("\n⚠️  Partial connectivity - check Dify configuration")
    else:
        print("\n🎉 All tests passed - ready for plugin installation!")
    
    return passed > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
