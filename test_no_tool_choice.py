#!/usr/bin/env python3
"""
Test không gửi tool_choice parameter
"""
import json
from openai import OpenAI

# Configuration
API_KEY = "sk-HMHa6NNBNnJdWfl_USuxuQ"
BASE_URL = "https://mkp-api.fptcloud.com"

def test_no_tool_choice_parameter():
    """Test gửi tools nhưng KHÔNG gửi tool_choice parameter"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get current weather information for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city name"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    print(f"🧪 Testing: Send tools but NO tool_choice parameter")
    print(f"📝 This should allow model to decide when to call functions")
    print(f"=" * 60)
    
    try:
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Use the available tools when appropriate to provide accurate information."
                },
                {
                    "role": "user",
                    "content": "What's the weather like in Hanoi today? Please use the weather function to get current information."
                }
            ],
            tools=tools,
            # IMPORTANT: NO tool_choice parameter at all
            temperature=0.1
        )
        
        print(f"✅ SUCCESS! Model responded without tool_choice parameter")
        print(f"📝 Response: {response.choices[0].message.content[:100] if response.choices[0].message.content else 'None'}...")
        
        if response.choices[0].message.tool_calls:
            print(f"🔧 EXCELLENT! Model decided to call tools:")
            for tool_call in response.choices[0].message.tool_calls:
                print(f"   - Function: {tool_call.function.name}")
                print(f"   - Arguments: {tool_call.function.arguments}")
            print(f"🎉 This means function calling will work with Dify!")
        else:
            print(f"⚠️  Model did not call tools (but no error occurred)")
            print(f"💡 This might be due to model's decision or prompt phrasing")
            
    except Exception as e:
        print(f"❌ FAILED: {str(e)}")
        print(f"💡 If this fails, we need a different approach")

def test_different_prompts():
    """Test với different prompts để trigger function calling"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    tools = [
        {
            "type": "function",
            "function": {
                "name": "calculate",
                "description": "Perform mathematical calculations",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "expression": {
                            "type": "string",
                            "description": "Mathematical expression to calculate"
                        }
                    },
                    "required": ["expression"]
                }
            }
        }
    ]
    
    prompts = [
        "Calculate 15 * 23 + 45. Use the calculate function.",
        "I need to compute 15 * 23 + 45. Please use the available calculation tool.",
        "What is 15 * 23 + 45? Use the calculator function to get the exact result.",
        "Please calculate 15 * 23 + 45 using the calculate function."
    ]
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\n🧪 Test {i}: Different prompt phrasing")
        print(f"📝 Prompt: {prompt}")
        print(f"-" * 40)
        
        try:
            response = client.chat.completions.create(
                model="QwQ-32B",
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                tools=tools,
                # NO tool_choice parameter
                temperature=0.1
            )
            
            if response.choices[0].message.tool_calls:
                print(f"✅ SUCCESS! Function called with prompt {i}")
                for tool_call in response.choices[0].message.tool_calls:
                    print(f"   - Function: {tool_call.function.name}")
                    print(f"   - Arguments: {tool_call.function.arguments}")
                break  # Found working prompt
            else:
                print(f"❌ No function call with prompt {i}")
                
        except Exception as e:
            print(f"❌ Error with prompt {i}: {str(e)}")

if __name__ == "__main__":
    print("🔍 Testing NO tool_choice Parameter Approach")
    print("=" * 60)
    print("Goal: Send tools but let FPT Cloud decide without explicit tool_choice")
    
    # Test basic approach
    test_no_tool_choice_parameter()
    
    # Test different prompts
    test_different_prompts()
    
    print(f"\n{'='*60}")
    print("🎯 CONCLUSION:")
    print("- If this works → Function calling will work with Dify")
    print("- If this fails → Need alternative approach")
    print("=" * 60)
