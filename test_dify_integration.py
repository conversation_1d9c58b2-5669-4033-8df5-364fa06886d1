#!/usr/bin/env python3
"""
Test script để kiểm tra tích hợp plugin với Dify
"""

import os
import sys
import json
import time
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Dify configuration
DIFY_BASE_URL = os.getenv("DIFY_BASE_URL", "http://localhost")
DIFY_API_KEY = os.getenv("DIFY_API_KEY", "")

# FPT Cloud configuration
FPT_API_KEY = os.getenv("API_KEY", "")

class DifyTester:
    def __init__(self):
        self.base_url = DIFY_BASE_URL
        self.api_key = DIFY_API_KEY
        self.session = requests.Session()
        
        if self.api_key:
            self.session.headers.update({
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            })

    def check_dify_health(self):
        """Check if Dify is running"""
        print("🔍 Checking Dify health...")
        
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                print("✅ Dify is running")
                return True
            else:
                print(f"❌ Dify health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to Dify: {str(e)}")
            return False

    def check_plugin_status(self):
        """Check if plugin is installed"""
        print("🔍 Checking plugin status...")
        
        try:
            # Try to access plugins endpoint
            response = self.session.get(f"{self.base_url}/console/api/plugins")
            
            if response.status_code == 200:
                plugins = response.json()
                
                # Look for our plugin
                for plugin in plugins.get('data', []):
                    if plugin.get('name') == 'aidibiz-fpt-cloud':
                        print(f"✅ Plugin found: {plugin.get('name')} v{plugin.get('version')}")
                        return True
                
                print("❌ Plugin not found in installed plugins")
                return False
            else:
                print(f"❌ Cannot access plugins endpoint: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Plugin status check error: {str(e)}")
            return False

    def check_model_providers(self):
        """Check if FPT Cloud provider is available"""
        print("🔍 Checking model providers...")
        
        try:
            response = self.session.get(f"{self.base_url}/console/api/workspaces/current/model-providers")
            
            if response.status_code == 200:
                providers = response.json()
                
                # Look for FPT Cloud provider
                for provider in providers.get('data', []):
                    if provider.get('provider') == 'fpt-cloud':
                        print(f"✅ FPT Cloud provider found")
                        print(f"   Status: {provider.get('status', 'unknown')}")
                        return True
                
                print("❌ FPT Cloud provider not found")
                print("Available providers:")
                for provider in providers.get('data', []):
                    print(f"   - {provider.get('provider')}")
                return False
            else:
                print(f"❌ Cannot access model providers: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Model providers check error: {str(e)}")
            return False

    def configure_provider(self):
        """Configure FPT Cloud provider"""
        print("🔍 Configuring FPT Cloud provider...")
        
        if not FPT_API_KEY:
            print("❌ FPT_API_KEY not found in environment")
            return False
        
        try:
            config_data = {
                "credentials": {
                    "api_key": FPT_API_KEY
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/console/api/workspaces/current/model-providers/fpt-cloud/credentials",
                json=config_data
            )
            
            if response.status_code in [200, 201]:
                print("✅ FPT Cloud provider configured successfully")
                return True
            else:
                print(f"❌ Provider configuration failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Provider configuration error: {str(e)}")
            return False

    def test_llm_model(self):
        """Test LLM model"""
        print("🔍 Testing LLM model (QwQ-32B)...")
        
        try:
            test_data = {
                "model": "fpt-cloud/QwQ-32B",
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello, please respond with just 'OK'"
                    }
                ],
                "max_tokens": 10
            }
            
            response = self.session.post(
                f"{self.base_url}/console/api/workspaces/current/model-providers/fpt-cloud/models/llm/QwQ-32B/test",
                json=test_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ LLM test successful")
                print(f"   Response: {result.get('content', 'No content')}")
                return True
            else:
                print(f"❌ LLM test failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ LLM test error: {str(e)}")
            return False

    def test_embedding_model(self):
        """Test embedding model"""
        print("🔍 Testing Embedding model (Vietnamese_Embedding)...")
        
        try:
            test_data = {
                "model": "fpt-cloud/Vietnamese_Embedding",
                "input": "Test embedding text"
            }
            
            response = self.session.post(
                f"{self.base_url}/console/api/workspaces/current/model-providers/fpt-cloud/models/text-embedding/Vietnamese_Embedding/test",
                json=test_data
            )
            
            if response.status_code == 200:
                result = response.json()
                embedding_dim = len(result.get('data', [{}])[0].get('embedding', []))
                print(f"✅ Embedding test successful")
                print(f"   Dimension: {embedding_dim}")
                return True
            else:
                print(f"❌ Embedding test failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Embedding test error: {str(e)}")
            return False

    def create_test_app(self):
        """Create a test app to verify plugin works end-to-end"""
        print("🔍 Creating test app...")
        
        try:
            app_data = {
                "name": "FPT Cloud Test App",
                "description": "Test app for FPT Cloud plugin",
                "mode": "chat",
                "model_config": {
                    "provider": "fpt-cloud",
                    "model": "QwQ-32B",
                    "parameters": {
                        "temperature": 0.7,
                        "max_tokens": 100
                    }
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/console/api/apps",
                json=app_data
            )
            
            if response.status_code in [200, 201]:
                app = response.json()
                app_id = app.get('id')
                print(f"✅ Test app created: {app_id}")
                return app_id
            else:
                print(f"❌ App creation failed: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ App creation error: {str(e)}")
            return None

def main():
    """Main test function"""
    print("🚀 Testing Dify Integration with AIDiBiz FPT Cloud Plugin")
    print("=" * 60)
    
    tester = DifyTester()
    
    tests = [
        ("Dify Health", tester.check_dify_health),
        ("Plugin Status", tester.check_plugin_status),
        ("Model Providers", tester.check_model_providers),
        ("Provider Configuration", tester.configure_provider),
        ("LLM Model Test", tester.test_llm_model),
        ("Embedding Model Test", tester.test_embedding_model),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        results[test_name] = test_func()
        
        # Add delay between tests
        time.sleep(2)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed!")
        print("\n🚀 Plugin is working correctly with Dify!")
        
        # Try to create test app
        app_id = tester.create_test_app()
        if app_id:
            print(f"\n✅ Test app created successfully: {app_id}")
            print(f"You can test it at: {DIFY_BASE_URL}/app/{app_id}")
    else:
        print("⚠️  Some integration tests failed.")
        print("Please check the Dify logs and plugin configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
