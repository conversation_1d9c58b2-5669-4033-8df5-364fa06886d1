#!/usr/bin/env python3
"""
Test if Cloudflare Workers AI plugin is ready for deployment
WITHOUT importing dify_plugin (to avoid import errors)
"""
import os
import re

def test_file_structure():
    """Test if all required files exist"""
    print("🔍 Testing File Structure...")
    
    required_files = [
        "manifest.yaml",
        "models/llm/llm.py",
        "models/text_embedding/text_embedding.py",
        "provider/cloudflare_workers_ai.py",
        "provider/cloudflare-workers-ai.yaml",
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0, missing_files

def test_llm_functions():
    """Test if LLM file has all required functions"""
    print("\n🔍 Testing LLM Functions...")
    
    llm_file = "models/llm/llm.py"
    if not os.path.exists(llm_file):
        print(f"❌ {llm_file} not found")
        return False, ["File not found"]
    
    with open(llm_file, 'r') as f:
        content = f.read()
    
    required_functions = [
        'def _invoke',
        'def get_num_tokens',
        'def validate_credentials',
        'def get_customizable_model_schema',
        'def get_model_mode',
        'def _num_tokens_from_messages',
        '_invoke_error_mapping',
    ]
    
    missing_functions = []
    for func in required_functions:
        if func in content:
            print(f"✅ {func}")
        else:
            print(f"❌ {func}")
            missing_functions.append(func)
    
    return len(missing_functions) == 0, missing_functions

def test_llm_imports():
    """Test if LLM file has correct imports"""
    print("\n🔍 Testing LLM Imports...")
    
    llm_file = "models/llm/llm.py"
    with open(llm_file, 'r') as f:
        content = f.read()
    
    required_imports = [
        'LLMMode',
        'LLMResult',
        'LLMResultChunk',
        'LLMResultChunkDelta',
        'CloudflareWorkersAILanguageModel',
    ]
    
    missing_imports = []
    for imp in required_imports:
        if imp in content:
            print(f"✅ {imp}")
        else:
            print(f"❌ {imp}")
            missing_imports.append(imp)
    
    return len(missing_imports) == 0, missing_imports

def test_llm_implementation():
    """Test specific implementation details"""
    print("\n🔍 Testing LLM Implementation Details...")
    
    llm_file = "models/llm/llm.py"
    with open(llm_file, 'r') as f:
        content = f.read()
    
    checks = [
        ('LLMMode.CHAT', 'get_model_mode returns LLMMode.CHAT'),
        ('@property', '_invoke_error_mapping is a property'),
        ('timeout_config = httpx.Timeout', 'Enhanced timeout configuration'),
        ('max_retries', 'Retry logic'),
        ('class CloudflareWorkersAILanguageModel', 'Correct class name'),
    ]
    
    failed_checks = []
    for pattern, description in checks:
        if pattern in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description}")
            failed_checks.append(description)
    
    return len(failed_checks) == 0, failed_checks

def test_manifest_yaml():
    """Test manifest.yaml content"""
    print("\n🔍 Testing Manifest YAML...")

    manifest_file = "manifest.yaml"
    if not os.path.exists(manifest_file):
        print(f"❌ {manifest_file} not found")
        return False, ["File not found"]

    with open(manifest_file, 'r') as f:
        content = f.read()

    # Check for key fields that should exist
    checks = [
        ('name: aidibiz_cloudflare_workers_ai', 'Plugin name'),
        ('version: 1.0.0', 'Plugin version'),
        ('type: plugin', 'Plugin type'),
        ('author: aidibiz-team', 'Plugin author'),
        ('llm: true', 'LLM support'),
        ('text_embedding: true', 'Text embedding support'),
        ('provider/cloudflare-workers-ai.yaml', 'Provider config'),
    ]

    failed_checks = []
    for pattern, description in checks:
        if pattern in content:
            print(f"✅ {description}")
        else:
            print(f"❌ {description}")
            failed_checks.append(description)

    return len(failed_checks) == 0, failed_checks

def test_provider_config():
    """Test provider configuration"""
    print("\n🔍 Testing Provider Configuration...")
    
    provider_py = "provider/cloudflare_workers_ai.py"
    provider_yaml = "provider/cloudflare-workers-ai.yaml"
    
    issues = []
    
    # Test Python provider file
    if os.path.exists(provider_py):
        print(f"✅ {provider_py}")
        with open(provider_py, 'r') as f:
            content = f.read()
        if 'CloudflareWorkersAIProvider' in content:
            print("✅ CloudflareWorkersAIProvider class found")
        else:
            print("❌ CloudflareWorkersAIProvider class not found")
            issues.append("Missing CloudflareWorkersAIProvider class")
    else:
        print(f"❌ {provider_py}")
        issues.append(f"Missing {provider_py}")
    
    # Test YAML provider file
    if os.path.exists(provider_yaml):
        print(f"✅ {provider_yaml}")
        with open(provider_yaml, 'r') as f:
            content = f.read()
        if 'provider: cloudflare-workers-ai' in content:
            print("✅ Provider name correct")
        else:
            print("❌ Provider name incorrect")
            issues.append("Incorrect provider name")
    else:
        print(f"❌ {provider_yaml}")
        issues.append(f"Missing {provider_yaml}")
    
    return len(issues) == 0, issues

def test_text_embedding():
    """Test text embedding model"""
    print("\n🔍 Testing Text Embedding Model...")
    
    embedding_file = "models/text_embedding/text_embedding.py"
    if not os.path.exists(embedding_file):
        print(f"❌ {embedding_file} not found")
        return False, ["File not found"]
    
    with open(embedding_file, 'r') as f:
        content = f.read()
    
    required_functions = [
        'def _invoke',
        'def get_num_tokens',
        'def validate_credentials',
        'def get_customizable_model_schema',
        '_invoke_error_mapping',
        'CloudflareWorkersAITextEmbeddingModel',
    ]
    
    missing_functions = []
    for func in required_functions:
        if func in content:
            print(f"✅ {func}")
        else:
            print(f"❌ {func}")
            missing_functions.append(func)
    
    return len(missing_functions) == 0, missing_functions

def main():
    """Run all tests"""
    print("🚀 Cloudflare Workers AI Plugin - Deployment Readiness Test")
    print("=" * 60)
    print(f"📁 Working Directory: {os.getcwd()}")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("LLM Functions", test_llm_functions),
        ("LLM Imports", test_llm_imports),
        ("LLM Implementation", test_llm_implementation),
        ("Manifest YAML", test_manifest_yaml),
        ("Provider Configuration", test_provider_config),
        ("Text Embedding Model", test_text_embedding),
    ]
    
    results = {}
    all_issues = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success, issues = test_func()
            results[test_name] = success
            if not success:
                all_issues.extend(issues)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
            all_issues.append(f"{test_name}: {e}")
    
    # Summary
    print("\n" + "="*60)
    print("📊 DEPLOYMENT READINESS SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Plugin is READY for deployment!")
        print("✅ All required files and functions present")
        print("✅ Structure follows Dify standards")
        print("✅ Can proceed with plugin packaging")
    else:
        print("⚠️ Plugin needs fixes before deployment")
        print("\n❌ Issues found:")
        for issue in all_issues:
            print(f"   - {issue}")
    
    print(f"\n📦 Plugin Package: aidibiz_cloudflare_workers_ai.signed.difypkg")
    print("🔧 Next Steps:")
    if passed == total:
        print("   1. Install plugin in Dify")
        print("   2. Add model with test credentials")
        print("   3. Test model functionality")
    else:
        print("   1. Fix the issues listed above")
        print("   2. Re-run this test")
        print("   3. Package plugin when all tests pass")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
