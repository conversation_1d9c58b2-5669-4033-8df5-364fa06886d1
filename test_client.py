#!/usr/bin/env python3
import os
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

API_KEY = os.getenv("API_KEY", "")
BASE_URL = os.getenv("BASE_URL", "https://mkp-api.fptcloud.com")
LLM_MODEL = os.getenv("LLM_MODEL", "QwQ-32B")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "Vietnamese_Embedding")

# Model configuration
MAX_TOKENS = int(os.getenv("MAX_TOKENS", "4096"))
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.7"))
TOP_P = float(os.getenv("TOP_P", "0.9"))
REQUEST_TIMEOUT = float(os.getenv("REQUEST_TIMEOUT", "120"))

# Khởi tạo client với timeout configuration
client = OpenAI(
    api_key=API_KEY,
    base_url=BASE_URL,
    timeout=REQUEST_TIMEOUT
)

def chat_stream(prompt: str):
    print(f"Testing LLM Model: {LLM_MODEL}")
    chat_completion = client.chat.completions.create(
        messages=[
            {
                "role": "system",
                "content": "You are a faithful Vietnamese assistant. Provide detailed answers. Use bullet points if necessary. Provide your answer in Vietnamese. Do not include any other text or instructions. Only provide the answer.",
            },
            {
                "role": "user",
                "content": f"{prompt}"
            },
        ],
        model=LLM_MODEL,
        temperature=TEMPERATURE,
        top_p=TOP_P,
        max_tokens=MAX_TOKENS,
        stream=True,  # this time, we set stream=True,
    )

    for chunk in chat_completion:
        if chunk is not None and chunk.choices[0].delta.content is not None:
            print(chunk.choices[0].delta.content, end='', flush=True)
    print("")

def chat_non_stream(prompt: str):
    print(f"Testing LLM Model: {LLM_MODEL}")
    chat_completion = client.chat.completions.create(
        messages=[
            {
                "role": "system",
                "content": "You are a faithful Vietnamese assistant. Provide detailed answers. Use bullet points if necessary. Provide your answer in Vietnamese. Do not include any other text or instructions. Only provide the answer.",
            },
            {
                "role": "user",
                "content": f"{prompt}"
            },
        ],
        model=LLM_MODEL,
        temperature=TEMPERATURE,
        top_p=TOP_P,
        max_tokens=MAX_TOKENS
    )

    print(chat_completion.choices[0].message.content)

def test_embedding(text: str):
    print(f"Testing Embedding Model: {EMBEDDING_MODEL}")
    response = client.embeddings.create(
        model=EMBEDDING_MODEL,
        input=text
    )

    embedding = response.data[0].embedding
    print(f"Embedding vector dimension: {len(embedding)}")
    print(f"First 10 dimensions: {embedding[:10]}")



# Test các model
print("=== Testing FPT Cloud AI Marketplace Models ===")
print()

print("1. Testing LLM (Chat):")
chat_non_stream("Bạn có thể giúp tôi mô tả về hệ mặt trời không?")
print()

print("2. Testing Embedding:")
test_embedding("Xin chào, tôi là một mô hình ngôn ngữ lớn. Tôi có thể giúp gì cho bạn hôm nay?")
print()

print("✅ All supported models tested successfully!")