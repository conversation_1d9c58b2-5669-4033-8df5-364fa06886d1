#!/usr/bin/env python3
"""
Test plugin functionality with mock scenarios
"""

import sys
import os
import importlib.util
from unittest.mock import Mock, patch

def load_module_from_path(module_name, file_path):
    """Load a Python module from file path"""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module

def test_provider_validation():
    """Test provider credential validation"""
    print("🔍 Testing Provider Credential Validation...")
    
    try:
        # Mock the dify_plugin imports
        with patch.dict('sys.modules', {
            'dify_plugin': Mock(),
            'dify_plugin.entities.model': Mock(),
            'dify_plugin.errors.model': Mock(),
        }):
            # Load the provider module
            provider_module = load_module_from_path(
                "cloudflare_workers_ai_provider",
                "provider/cloudflare_workers_ai.py"
            )
            
            # Test that the class exists and has required methods
            provider_class = getattr(provider_module, 'CloudflareWorkersAIProvider', None)
            if not provider_class:
                print("❌ CloudflareWorkersAIProvider class not found")
                return False
            
            # Check if required methods exist
            required_methods = ['validate_provider_credentials']
            for method in required_methods:
                if not hasattr(provider_class, method):
                    print(f"❌ Missing method: {method}")
                    return False
            
            print("✅ Provider class structure is valid")
            return True
            
    except Exception as e:
        print(f"❌ Error testing provider: {e}")
        return False

def test_llm_model_structure():
    """Test LLM model class structure"""
    print("🔍 Testing LLM Model Structure...")
    
    try:
        # Mock the dify_plugin imports
        mock_modules = {
            'dify_plugin': Mock(),
            'dify_plugin.entities.model': Mock(),
            'dify_plugin.entities.model.llm': Mock(),
            'dify_plugin.entities.model.message': Mock(),
            'dify_plugin.errors.model': Mock(),
            'dify_plugin.interfaces.model.large_language_model': Mock(),
            'httpx': Mock(),
        }
        
        with patch.dict('sys.modules', mock_modules):
            # Load the LLM module
            llm_module = load_module_from_path(
                "cloudflare_workers_ai_llm",
                "models/llm/cloudflare_workers_ai_llm.py"
            )
            
            # Test that the class exists and has required methods
            llm_class = getattr(llm_module, 'CloudflareWorkersAILanguageModel', None)
            if not llm_class:
                print("❌ CloudflareWorkersAILanguageModel class not found")
                return False
            
            # Check if required methods exist
            required_methods = [
                '_invoke', 'get_num_tokens', 'validate_credentials', 
                'get_customizable_model_schema', '_generate'
            ]
            for method in required_methods:
                if not hasattr(llm_class, method):
                    print(f"❌ Missing method: {method}")
                    return False
            
            print("✅ LLM model class structure is valid")
            return True
            
    except Exception as e:
        print(f"❌ Error testing LLM model: {e}")
        return False

def test_embedding_model_structure():
    """Test text embedding model class structure"""
    print("🔍 Testing Text Embedding Model Structure...")
    
    try:
        # Mock the dify_plugin imports
        mock_modules = {
            'dify_plugin': Mock(),
            'dify_plugin.entities.model': Mock(),
            'dify_plugin.entities.model.text_embedding': Mock(),
            'dify_plugin.errors.model': Mock(),
            'dify_plugin.interfaces.model.text_embedding_model': Mock(),
            'httpx': Mock(),
        }
        
        with patch.dict('sys.modules', mock_modules):
            # Load the embedding module
            embedding_module = load_module_from_path(
                "cloudflare_workers_ai_embedding",
                "models/text_embedding/cloudflare_workers_ai_text_embedding.py"
            )
            
            # Test that the class exists and has required methods
            embedding_class = getattr(embedding_module, 'CloudflareWorkersAITextEmbeddingModel', None)
            if not embedding_class:
                print("❌ CloudflareWorkersAITextEmbeddingModel class not found")
                return False
            
            # Check if required methods exist
            required_methods = [
                '_invoke', 'get_num_tokens', 'validate_credentials', 
                'get_customizable_model_schema'
            ]
            for method in required_methods:
                if not hasattr(embedding_class, method):
                    print(f"❌ Missing method: {method}")
                    return False
            
            print("✅ Text embedding model class structure is valid")
            return True
            
    except Exception as e:
        print(f"❌ Error testing embedding model: {e}")
        return False

def test_configuration_schema():
    """Test model configuration schema"""
    print("🔍 Testing Configuration Schema...")
    
    try:
        # Test that the YAML configuration has all required fields
        import yaml
        
        with open("provider/cloudflare-workers-ai.yaml", "r") as f:
            config = yaml.safe_load(f)
        
        # Check model credential schema
        if "model_credential_schema" not in config:
            print("❌ Missing model_credential_schema")
            return False
        
        credential_schemas = config["model_credential_schema"]["credential_form_schemas"]
        
        # Check for required credential fields
        required_variables = ["account_id", "context_size", "max_tokens", "vision_support", "function_call_support"]
        found_variables = [schema.get("variable") for schema in credential_schemas if "variable" in schema]
        
        missing_variables = [var for var in required_variables if var not in found_variables]
        if missing_variables:
            print(f"❌ Missing credential variables: {missing_variables}")
            return False
        
        # Check provider credential schema
        if "provider_credential_schema" not in config:
            print("❌ Missing provider_credential_schema")
            return False
        
        provider_schemas = config["provider_credential_schema"]["credential_form_schemas"]
        provider_variables = [schema.get("variable") for schema in provider_schemas if "variable" in schema]
        
        if "api_token" not in provider_variables:
            print("❌ Missing api_token in provider credentials")
            return False
        
        print("✅ Configuration schema is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration schema: {e}")
        return False

def test_requirements():
    """Test requirements.txt"""
    print("🔍 Testing Requirements...")
    
    try:
        with open("requirements.txt", "r") as f:
            requirements = f.read().strip().split('\n')
        
        required_packages = ["dify_plugin", "httpx"]
        
        for package in required_packages:
            found = any(package in req for req in requirements)
            if not found:
                print(f"❌ Missing required package: {package}")
                return False
        
        print("✅ Requirements are valid")
        return True
        
    except Exception as e:
        print(f"❌ Error testing requirements: {e}")
        return False

def run_functionality_tests():
    """Run all functionality tests"""
    print("🧪 Cloudflare Workers AI Plugin Functionality Tests")
    print("=" * 70)
    
    tests = [
        ("Provider Validation", test_provider_validation),
        ("LLM Model Structure", test_llm_model_structure),
        ("Embedding Model Structure", test_embedding_model_structure),
        ("Configuration Schema", test_configuration_schema),
        ("Requirements", test_requirements),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 FUNCTIONALITY TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<40} {status}")
    
    print("-" * 70)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    
    if passed == total:
        print("\n🎉 ALL FUNCTIONALITY TESTS PASSED!")
        print("Plugin implementation is structurally sound and ready for integration.")
        return True
    else:
        print(f"\n⚠️  {total - passed} functionality test(s) failed.")
        return False

if __name__ == "__main__":
    success = run_functionality_tests()
    sys.exit(0 if success else 1)
