# 🔧 Cloudflare Workers AI Plugin - Troubleshooting Guide

## ❌ Vấn đề: "Không thêm được model"

### 🔍 Nguyên nhân có thể:

#### 1. **Thiếu hàm `validate_credentials`**
- **Triệu chứng**: Model không xuất hiện sau khi submit
- **Nguyên nhân**: Dify yêu c<PERSON> hà<PERSON> `validate_credentials` để kiểm tra credentials khi user submit model
- **Gi<PERSON>i pháp**: ✅ **Đ<PERSON> khắc phục** - Thêm hàm `validate_credentials` robust với error handling chi tiết

#### 2. **Thiếu `_invoke_error_mapping` property**
- **Triệu chứng**: Plugin crash khi có lỗi API
- **Nguyên nhân**: Dify cần mapping errors để xử lý thống nhất
- **Giải pháp**: ✅ **<PERSON><PERSON> khắ<PERSON> phục** - Thêm `_invoke_error_mapping` cho cả LLM và Text Embedding

#### 3. **Credentials không hợp lệ**
- **<PERSON><PERSON>u chứng**: Validation failed khi submit
- **Nguyên nhân**: API Token hoặc Account ID sai
- **Giải pháp**: Kiểm tra credentials

#### 4. **Model name không đúng**
- **Triệu chứng**: 404 Not Found error
- **Nguyên nhân**: Tên model không tồn tại trong Cloudflare Workers AI
- **Giải pháp**: Sử dụng tên model chính xác

### 🛠️ Cách khắc phục:

#### **Bước 1: Kiểm tra Plugin Structure**
```bash
cd cloudflare-workers-ai
python3 test_structure.py
```

**Kết quả mong đợi:**
```
✅ All required files present
✅ Manifest structure valid
✅ Provider configuration valid
✅ Supported model types correct
```

#### **Bước 2: Test API Credentials**
```bash
python3 test_cloudflare_direct.py
```

**Kết quả mong đợi:**
```
Basic Chat           ✅ PASS
Streaming Chat       ✅ PASS
Function Calling     ✅ PASS
Text Embedding       ✅ PASS
```

#### **Bước 3: Kiểm tra Validate Credentials**
```bash
python3 test_validate_credentials.py
```

**Kết quả mong đợi:**
```
✅ validate_credentials passed!
✅ Correctly caught missing API token
✅ Correctly caught missing account ID
```

### 📋 Checklist khắc phục:

#### ✅ **Plugin Structure** (Đã hoàn thành)
- [x] Có file `__init__.py` trong tất cả thư mục models
- [x] Tên file models theo chuẩn: `llm.py`, `text_embedding.py`
- [x] Provider YAML configuration đúng format
- [x] Manifest.yaml theo chuẩn Dify

#### ✅ **Required Functions** (Đã hoàn thành)
- [x] `validate_credentials()` với error handling chi tiết
- [x] `_invoke_error_mapping` property
- [x] `get_customizable_model_schema()` 
- [x] `_invoke()` method với streaming support
- [x] `get_num_tokens()` method

#### ✅ **Error Handling** (Đã hoàn thành)
- [x] Proper exception mapping
- [x] Detailed error messages
- [x] Credential validation errors
- [x] API error transformation

### 🔧 Cấu hình Model trong Dify:

#### **LLM Model Configuration:**
```yaml
Provider: Cloudflare Workers AI
Model Type: LLM
Model Name: @cf/qwen/qwq-32b
API Token: jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd
Account ID: ********************************
Context Size: 32768
Max Tokens: 4096
Vision Support: No
Function Call Support: Yes
```

#### **Text Embedding Model Configuration:**
```yaml
Provider: Cloudflare Workers AI
Model Type: Text Embedding
Model Name: @cf/baai/bge-large-en-v1.5
API Token: jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd
Account ID: ********************************
Context Size: 8192
```

### 🚨 Common Errors và Solutions:

#### **Error: "API token is required"**
- **Nguyên nhân**: Thiếu API Token
- **Giải pháp**: Nhập API Token từ Cloudflare Dashboard

#### **Error: "Account ID is required"**
- **Nguyên nhân**: Thiếu Account ID
- **Giải pháp**: Nhập Account ID từ Cloudflare Dashboard

#### **Error: "Model not found"**
- **Nguyên nhân**: Tên model sai
- **Giải pháp**: Kiểm tra tên model tại [Cloudflare Workers AI Models](https://developers.cloudflare.com/workers-ai/models/)

#### **Error: "Invalid API token"**
- **Nguyên nhân**: API Token không hợp lệ hoặc hết hạn
- **Giải pháp**: Tạo API Token mới từ Cloudflare Dashboard

#### **Error: "Access forbidden"**
- **Nguyên nhân**: Account không có quyền truy cập Workers AI
- **Giải pháp**: Kiểm tra subscription và permissions

### 📚 Supported Models:

#### **LLM Models:**
- `@cf/qwen/qwq-32b` ✅ (Tested)
- `@cf/meta/llama-3.1-8b-instruct` ✅
- `@cf/meta/llama-3.2-11b-vision-instruct` ✅ (Vision)
- `@cf/mistral/mistral-7b-instruct-v0.2` ✅

#### **Text Embedding Models:**
- `@cf/baai/bge-large-en-v1.5` ✅ (Tested)
- `@cf/baai/bge-base-en-v1.5` ✅
- `@cf/baai/bge-small-en-v1.5` ✅
- `@cf/baai/bge-m3` ✅

### 🔄 Plugin Update Process:

1. **Sửa code** trong `cloudflare-workers-ai/`
2. **Test changes**: `python3 test_structure.py`
3. **Repackage**: `./package_cloudflare_plugin.sh`
4. **Reinstall** plugin trong Dify
5. **Test model** addition

### 📞 Support:

Nếu vẫn gặp vấn đề, hãy kiểm tra:
1. **Dify logs** trong container
2. **Plugin daemon logs**
3. **API response** từ Cloudflare
4. **Network connectivity** đến Cloudflare API

---

**Plugin Version**: v0.0.1  
**Last Updated**: 25/12/2024  
**Status**: ✅ Production Ready
