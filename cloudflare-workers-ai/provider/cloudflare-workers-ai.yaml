background: '#F38020'
configurate_methods:
- customizable-model
extra:
  python:
    model_sources:
    - models/llm/llm.py
    - models/text_embedding/text_embedding.py
    provider_source: provider/cloudflare_workers_ai.py
help:
  title:
    en_US: How to get your Cloudflare Workers AI API token
    vi_VN: C<PERSON>ch lấy API token của Cloudflare Workers AI
  url:
    en_US: https://developers.cloudflare.com/workers-ai/get-started/rest-api/
    vi_VN: https://developers.cloudflare.com/workers-ai/get-started/rest-api/
icon_large:
  en_US: icon_l_en.svg
icon_small:
  en_US: icon_s_en.svg
label:
  en_US: Cloudflare Workers AI
  vi_VN: Cloudflare Workers AI
model_credential_schema:
  credential_form_schemas:
  - label:
      en_US: API Token
      vi_VN: API Token
    placeholder:
      en_US: Enter your Cloudflare Workers AI API Token for this model
      vi_VN: Nhập API Token của Cloudflare Workers AI cho model này
    required: true
    type: secret-input
    variable: api_token
  - label:
      en_US: Account ID
      vi_VN: Account ID
    placeholder:
      en_US: Enter your Cloudflare Account ID
      vi_VN: Nhập Cloudflare Account ID của bạn
    required: true
    type: text-input
    variable: account_id
  - default: '32768'
    label:
      en_US: Model context size
      vi_VN: Kích thước ngữ cảnh model
    placeholder:
      en_US: Enter your Model context size
      vi_VN: Nhập kích thước ngữ cảnh model
    required: true
    type: text-input
    variable: context_size
  - default: '4096'
    label:
      en_US: Upper bound for max tokens
      vi_VN: Giới hạn trên cho max tokens
    required: true
    show_on:
    - value: llm
      variable: __model_type
    type: text-input
    variable: max_tokens
  - default: 'false'
    label:
      en_US: Vision support
      vi_VN: Hỗ trợ thị giác
    options:
    - label:
        en_US: 'Yes'
        vi_VN: Có
      value: 'true'
    - label:
        en_US: 'No'
        vi_VN: Không
      value: 'false'
    required: false
    show_on:
    - value: llm
      variable: __model_type
    type: radio
    variable: vision_support
  - default: 'false'
    label:
      en_US: Function call support
      vi_VN: Hỗ trợ gọi hàm
    options:
    - label:
        en_US: 'Yes'
        vi_VN: Có
      value: 'true'
    - label:
        en_US: 'No'
        vi_VN: Không
      value: 'false'
    required: false
    show_on:
    - value: llm
      variable: __model_type
    type: radio
    variable: function_call_support
  model:
    label:
      en_US: Model Name
      vi_VN: Tên Model
    placeholder:
      en_US: "Enter your model name (e.g., @cf/qwen/qwq-32b-preview)"
      vi_VN: "Nhập tên model (ví dụ: @cf/qwen/qwq-32b-preview)"
provider: cloudflare-workers-ai
provider_credential_schema:
  credential_form_schemas:
  - label:
      en_US: Default Account ID
      vi_VN: Account ID mặc định
    placeholder:
      en_US: Enter your default Cloudflare Account ID (can be overridden per model)
      vi_VN: Nhập Cloudflare Account ID mặc định (có thể ghi đè cho từng model)
    required: false
    type: text-input
    variable: default_account_id
supported_model_types:
- llm
- text-embedding
