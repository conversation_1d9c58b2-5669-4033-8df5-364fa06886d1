#!/usr/bin/env python3
"""
Simple test for Cloudflare Workers AI plugin readiness
"""
import os

def main():
    """Run simple readiness test"""
    print("🚀 Cloudflare Workers AI Plugin - Simple Readiness Test")
    print("=" * 60)
    print(f"📁 Working Directory: {os.getcwd()}")
    print("=" * 60)
    
    # Test 1: File Structure
    print("\n🔍 Testing File Structure...")
    required_files = [
        "manifest.yaml",
        "models/llm/llm.py", 
        "models/text_embedding/text_embedding.py",
        "provider/cloudflare_workers_ai.py",
        "provider/cloudflare-workers-ai.yaml",
    ]
    
    files_ok = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            files_ok = False
    
    # Test 2: LLM Functions
    print("\n🔍 Testing LLM Functions...")
    llm_file = "models/llm/llm.py"
    if os.path.exists(llm_file):
        with open(llm_file, 'r') as f:
            llm_content = f.read()
        
        required_functions = [
            'def _invoke',
            'def get_num_tokens',
            'def validate_credentials', 
            'def get_customizable_model_schema',
            'def get_model_mode',
            'def _num_tokens_from_messages',
            '_invoke_error_mapping',
            'class CloudflareWorkersAILanguageModel',
        ]
        
        functions_ok = True
        for func in required_functions:
            if func in llm_content:
                print(f"✅ {func}")
            else:
                print(f"❌ {func}")
                functions_ok = False
    else:
        functions_ok = False
    
    # Test 3: Implementation Details
    print("\n🔍 Testing Implementation Details...")
    implementation_ok = True
    if os.path.exists(llm_file):
        checks = [
            ('return LLMMode.CHAT', 'get_model_mode returns LLMMode.CHAT'),
            ('@property', '_invoke_error_mapping is property'),
            ('timeout_config = httpx.Timeout', 'Enhanced timeout config'),
            ('max_retries', 'Retry logic'),
        ]
        
        for pattern, description in checks:
            if pattern in llm_content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description}")
                implementation_ok = False
    else:
        implementation_ok = False
    
    # Test 4: Manifest
    print("\n🔍 Testing Manifest...")
    manifest_ok = True
    if os.path.exists("manifest.yaml"):
        with open("manifest.yaml", 'r') as f:
            manifest_content = f.read()
        
        manifest_checks = [
            ('name: aidibiz_cloudflare_workers_ai', 'Plugin name'),
            ('version: 0.0.1', 'Plugin version'),
            ('type: plugin', 'Plugin type'),
            ('llm: true', 'LLM support'),
            ('text_embedding: true', 'Text embedding support'),
        ]
        
        for pattern, description in manifest_checks:
            if pattern in manifest_content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description}")
                manifest_ok = False
    else:
        manifest_ok = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 SUMMARY")
    print("="*60)
    
    all_tests = [
        ("File Structure", files_ok),
        ("LLM Functions", functions_ok),
        ("Implementation", implementation_ok),
        ("Manifest", manifest_ok),
    ]
    
    passed = 0
    for test_name, result in all_tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    total = len(all_tests)
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Plugin is READY!")
        print("✅ All required components present")
        print("✅ Can proceed with deployment")
        
        print("\n🔧 Next Steps:")
        print("   1. Package plugin: ./package_cloudflare_plugin.sh")
        print("   2. Install in Dify")
        print("   3. Test with credentials:")
        print("      - Model: @cf/qwen/qwq-32b")
        print("      - API Token: jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd")
        print("      - Account ID: 3566bb7911a7d24dcfb1fe4589f9427b")
        
        return True
    else:
        print("⚠️ Plugin needs fixes")
        print("🔧 Fix the failed tests above")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
