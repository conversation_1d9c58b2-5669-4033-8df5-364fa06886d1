#!/usr/bin/env python3
"""
Test validate_credentials function for Cloudflare Workers AI plugin
"""
import sys
import os

# Add the models directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'models', 'llm'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'models', 'text_embedding'))

# Mock the dify_plugin imports since we're testing outside Dify
class MockI18nObject:
    def __init__(self, en_US=None, vi_VN=None):
        self.en_US = en_US
        self.vi_VN = vi_VN

class MockParameterRule:
    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)

class MockAIModelEntity:
    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)

class MockLLMResult:
    def __init__(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)

class MockAssistantPromptMessage:
    def __init__(self, content=""):
        self.content = content

class MockUserPromptMessage:
    def __init__(self, content=""):
        self.content = content

class MockCredentialsValidateFailedError(Exception):
    pass

class MockInvokeError(Exception):
    pass

class MockInvokeAuthorizationError(MockInvokeError):
    pass

class MockInvokeBadRequestError(MockInvokeError):
    pass

class MockInvokeConnectionError(MockInvokeError):
    pass

class MockInvokeRateLimitError(MockInvokeError):
    pass

class MockInvokeServerUnavailableError(MockInvokeError):
    pass

class MockLargeLanguageModel:
    def _num_tokens_from_messages(self, messages, tools=None):
        return 10
    
    def _calc_response_usage(self, **kwargs):
        return {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}

# Mock the imports
import sys
sys.modules['dify_plugin.entities.model'] = type('MockModule', (), {
    'I18nObject': MockI18nObject,
    'ParameterRule': MockParameterRule,
    'ParameterType': type('ParameterType', (), {'FLOAT': 'float', 'INT': 'int'}),
    'AIModelEntity': MockAIModelEntity,
    'FetchFrom': type('FetchFrom', (), {'CUSTOMIZABLE_MODEL': 'customizable'}),
    'ModelType': type('ModelType', (), {'LLM': 'llm'}),
    'LLMMode': type('LLMMode', (), {'CHAT': type('Chat', (), {'value': 'chat'})}),
    'ModelFeature': type('ModelFeature', (), {'VISION': 'vision', 'TOOL_CALL': 'tool_call'}),
    'ModelPropertyKey': type('ModelPropertyKey', (), {'MODE': 'mode', 'CONTEXT_SIZE': 'context_size'}),
})

sys.modules['dify_plugin.entities.model.llm'] = type('MockModule', (), {
    'LLMResult': MockLLMResult,
    'LLMResultChunk': type('LLMResultChunk', (), {}),
    'LLMResultChunkDelta': type('LLMResultChunkDelta', (), {}),
})

sys.modules['dify_plugin.entities.model.message'] = type('MockModule', (), {
    'AssistantPromptMessage': MockAssistantPromptMessage,
    'UserPromptMessage': MockUserPromptMessage,
    'PromptMessage': type('PromptMessage', (), {}),
    'PromptMessageTool': type('PromptMessageTool', (), {}),
    'SystemPromptMessage': type('SystemPromptMessage', (), {}),
    'ToolPromptMessage': type('ToolPromptMessage', (), {}),
    'PromptMessageContentType': type('PromptMessageContentType', (), {'TEXT': 'text', 'IMAGE': 'image'}),
})

sys.modules['dify_plugin.errors.model'] = type('MockModule', (), {
    'CredentialsValidateFailedError': MockCredentialsValidateFailedError,
    'InvokeError': MockInvokeError,
    'InvokeAuthorizationError': MockInvokeAuthorizationError,
    'InvokeBadRequestError': MockInvokeBadRequestError,
    'InvokeConnectionError': MockInvokeConnectionError,
    'InvokeRateLimitError': MockInvokeRateLimitError,
    'InvokeServerUnavailableError': MockInvokeServerUnavailableError,
})

sys.modules['dify_plugin.interfaces.model.large_language_model'] = type('MockModule', (), {
    'LargeLanguageModel': MockLargeLanguageModel,
})

def test_validate_credentials():
    """Test the validate_credentials function"""
    print("🧪 Testing validate_credentials function...")
    
    # Test configuration
    test_credentials = {
        "api_token": "jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd",
        "account_id": "3566bb7911a7d24dcfb1fe4589f9427b",
        "context_size": "32768",
        "max_tokens": "4096",
        "vision_support": "false",
        "function_call_support": "true"
    }
    
    test_model = "@cf/qwen/qwq-32b"
    
    try:
        # Import the LLM class
        from llm import CloudflareWorkersAILanguageModel
        
        # Create instance
        llm_model = CloudflareWorkersAILanguageModel()
        
        print(f"✅ Successfully imported CloudflareWorkersAILanguageModel")
        print(f"🔍 Testing with model: {test_model}")
        print(f"🔑 Testing with credentials: API token ending in ...{test_credentials['api_token'][-4:]}")
        
        # Test validate_credentials
        try:
            llm_model.validate_credentials(test_model, test_credentials)
            print("✅ validate_credentials passed!")
            return True
        except Exception as e:
            print(f"❌ validate_credentials failed: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import LLM model: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_missing_credentials():
    """Test validate_credentials with missing credentials"""
    print("\n🧪 Testing validate_credentials with missing credentials...")
    
    try:
        from llm import CloudflareWorkersAILanguageModel
        llm_model = CloudflareWorkersAILanguageModel()
        
        # Test with missing API token
        try:
            llm_model.validate_credentials("@cf/qwen/qwq-32b", {"account_id": "test"})
            print("❌ Should have failed with missing API token")
            return False
        except MockCredentialsValidateFailedError as e:
            if "API token is required" in str(e):
                print("✅ Correctly caught missing API token")
            else:
                print(f"❌ Wrong error message: {e}")
                return False
        
        # Test with missing account ID
        try:
            llm_model.validate_credentials("@cf/qwen/qwq-32b", {"api_token": "test"})
            print("❌ Should have failed with missing account ID")
            return False
        except MockCredentialsValidateFailedError as e:
            if "Account ID is required" in str(e):
                print("✅ Correctly caught missing account ID")
            else:
                print(f"❌ Wrong error message: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Cloudflare Workers AI Plugin - validate_credentials Test")
    print("=" * 60)
    
    tests = [
        ("Validate Credentials", test_validate_credentials),
        ("Missing Credentials", test_missing_credentials),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! validate_credentials function is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
