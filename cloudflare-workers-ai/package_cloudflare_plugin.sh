#!/bin/bash

# Script để đóng gói và ký Cloudflare Workers AI plugin cho Dify
# Tác giả: AIDBiz Team
# Ngày: 25/12/2024

set -e  # Dừng script nếu có lỗi

# Màu sắc cho output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Thông tin plugin
PLUGIN_NAME="aidibiz_cloudflare_workers_ai"
PLUGIN_VERSION=$(grep -E "^version:" manifest.yaml | awk '{print $2}')
KEY_PAIR_NAME="aidbiz_key_pair"
DIFY_CLI="./dify"
DIFY_CLI_VERSION="0.1.0"

# Banner
show_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║        🚀 CLOUDFLARE WORKERS AI PLUGIN PACKAGER 🚀          ║"
    echo "║                                                              ║"
    echo "║                    AIDiBiz Team - 2024                       ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Kiểm tra hệ điều hành
check_os() {
    echo -e "${YELLOW}🔍 Kiểm tra hệ điều hành...${NC}"
    
    OS="$(uname -s)"
    ARCH="$(uname -m)"
    
    case "${OS}" in
        Linux*)     OS_TYPE=linux;;
        Darwin*)    OS_TYPE=darwin;;
        *)          echo -e "${RED}❌ Hệ điều hành không được hỗ trợ: ${OS}${NC}" && exit 1;;
    esac
    
    case "${ARCH}" in
        x86_64*)    ARCH_TYPE=amd64;;
        arm64*)     ARCH_TYPE=arm64;;
        *)          echo -e "${RED}❌ Kiến trúc không được hỗ trợ: ${ARCH}${NC}" && exit 1;;
    esac
    
    echo -e "${GREEN}✅ Đã phát hiện: ${OS_TYPE}-${ARCH_TYPE}${NC}"
}

# Kiểm tra cấu trúc plugin
validate_plugin_structure() {
    echo -e "${YELLOW}🔍 Kiểm tra cấu trúc plugin...${NC}"
    
    required_files=(
        "manifest.yaml"
        "main.py"
        "requirements.txt"
        "provider/cloudflare-workers-ai.yaml"
        "provider/cloudflare_workers_ai.py"
        "models/llm/llm.py"
        "models/text_embedding/text_embedding.py"
        "_assets/icon_s_en.svg"
    )
    
    missing_files=()
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -ne 0 ]; then
        echo -e "${RED}❌ Thiếu các file bắt buộc:${NC}"
        for file in "${missing_files[@]}"; do
            echo -e "${RED}   - $file${NC}"
        done
        exit 1
    fi
    
    echo -e "${GREEN}✅ Cấu trúc plugin hợp lệ${NC}"
}

# Chạy test API
run_api_tests() {
    echo -e "${YELLOW}🧪 Chạy test API Cloudflare Workers AI...${NC}"
    
    if [ -f "test_cloudflare_direct.py" ]; then
        echo -e "${BLUE}📋 Kết quả test API:${NC}"
        python3 test_cloudflare_direct.py | tail -10
        echo -e "${GREEN}✅ Test API hoàn thành${NC}"
    else
        echo -e "${YELLOW}⚠️ Không tìm thấy file test API${NC}"
    fi
}

# Tải công cụ Dify CLI
download_dify_cli() {
    if [ -f "$DIFY_CLI" ]; then
        echo -e "${GREEN}✅ Công cụ Dify CLI đã tồn tại.${NC}"
        return
    fi
    
    echo -e "${YELLOW}📥 Tải công cụ Dify CLI...${NC}"
    DOWNLOAD_URL="https://github.com/langgenius/dify-plugin-daemon/releases/download/${DIFY_CLI_VERSION}/dify-plugin-${OS_TYPE}-${ARCH_TYPE}"
    
    curl -L -o "$DIFY_CLI" "$DOWNLOAD_URL"
    chmod +x "$DIFY_CLI"
    
    echo -e "${GREEN}✅ Đã tải công cụ Dify CLI thành công.${NC}"
}

# Tạo cặp khóa
generate_key_pair() {
    if [ -f "${KEY_PAIR_NAME}.private.pem" ] && [ -f "${KEY_PAIR_NAME}.public.pem" ]; then
        echo -e "${GREEN}✅ Cặp khóa đã tồn tại.${NC}"
        return
    fi
    
    echo -e "${YELLOW}🔐 Tạo cặp khóa mới...${NC}"
    "$DIFY_CLI" signature generate -f "$KEY_PAIR_NAME"
    
    echo -e "${GREEN}✅ Đã tạo cặp khóa thành công.${NC}"
}

# Tạo file .difyignore
create_difyignore() {
    echo -e "${YELLOW}📝 Tạo file .difyignore...${NC}"
    cat > .difyignore << 'EOF'
# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd

# Environment files
.env
.env.*

# System files
.DS_Store
Thumbs.db

# Virtual environments
.venv/
venv/
env/

# Binary files
dify

# Git directory
.git/
.gitignore

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Temporary files
*.tmp
*.bak
*~

# Log files
*.log

# Package files
*.difypkg
*.signed.difypkg

# Test files
test_*.py
*_test.py
test_structure.py
test_cloudflare_direct.py

# Documentation
README.md
CHANGELOG.md
*.md

# Scripts
package_*.sh
*.sh

# Key files
*.pem
EOF
    echo -e "${GREEN}✅ Đã tạo file .difyignore${NC}"
}

# Đóng gói plugin
package_plugin() {
    echo -e "${YELLOW}📦 Đóng gói plugin...${NC}"
    
    create_difyignore
    
    # Đóng gói plugin
    "$DIFY_CLI" plugin package . -o "${PLUGIN_NAME}.difypkg"
    
    echo -e "${GREEN}✅ Đã đóng gói plugin thành công: ${PLUGIN_NAME}.difypkg${NC}"
}

# Ký plugin
sign_plugin() {
    echo -e "${YELLOW}✍️ Ký plugin...${NC}"
    
    "$DIFY_CLI" signature sign "${PLUGIN_NAME}.difypkg" -p "${KEY_PAIR_NAME}.private.pem"
    
    echo -e "${GREEN}✅ Đã ký plugin thành công: ${PLUGIN_NAME}.signed.difypkg${NC}"
}

# Xác minh chữ ký
verify_signature() {
    echo -e "${YELLOW}🔍 Xác minh chữ ký...${NC}"
    
    "$DIFY_CLI" signature verify "${PLUGIN_NAME}.signed.difypkg" -p "${KEY_PAIR_NAME}.public.pem"
    
    echo -e "${GREEN}✅ Đã xác minh chữ ký thành công.${NC}"
}

# Hiển thị thông tin plugin
show_plugin_info() {
    echo -e "\n${BLUE}📋 THÔNG TIN PLUGIN${NC}"
    echo -e "${BLUE}═══════════════════════════════════════${NC}"
    echo -e "${YELLOW}Tên plugin:${NC} ${PLUGIN_NAME}"
    echo -e "${YELLOW}Phiên bản:${NC} ${PLUGIN_VERSION}"
    echo -e "${YELLOW}Loại:${NC} Cloudflare Workers AI Provider"
    echo -e "${YELLOW}Hỗ trợ:${NC} LLM, Text Embedding, Function Calling"
    echo -e "${YELLOW}File đóng gói:${NC} ${PLUGIN_NAME}.signed.difypkg"
    
    # Hiển thị kích thước file
    if [ -f "${PLUGIN_NAME}.signed.difypkg" ]; then
        SIZE=$(ls -lh "${PLUGIN_NAME}.signed.difypkg" | awk '{print $5}')
        echo -e "${YELLOW}Kích thước:${NC} ${SIZE}"
    fi
}

# Hiển thị hướng dẫn cài đặt
show_installation_guide() {
    echo -e "\n${GREEN}🚀 HƯỚNG DẪN CÀI ĐẶT PLUGIN${NC}"
    echo -e "${GREEN}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${YELLOW}1. Cấu hình máy chủ Dify để chấp nhận chữ ký:${NC}"
    echo -e "   ${BLUE}a. Tạo thư mục cho khóa công khai:${NC}"
    echo -e "      mkdir -p docker/volumes/plugin_daemon/public_keys"
    echo -e "      cp ${KEY_PAIR_NAME}.public.pem docker/volumes/plugin_daemon/public_keys/"
    echo -e "\n   ${BLUE}b. Cấu hình docker-compose.override.yaml:${NC}"
    echo -e "      services:"
    echo -e "        plugin_daemon:"
    echo -e "          environment:"
    echo -e "            FORCE_VERIFYING_SIGNATURE: true"
    echo -e "            THIRD_PARTY_SIGNATURE_VERIFICATION_ENABLED: true"
    echo -e "            THIRD_PARTY_SIGNATURE_VERIFICATION_PUBLIC_KEYS: /app/storage/public_keys/${KEY_PAIR_NAME}.public.pem"
    echo -e "\n   ${BLUE}c. Khởi động lại Dify:${NC}"
    echo -e "      cd docker && docker compose down && docker compose up -d"
    echo -e "\n${YELLOW}2. Cài đặt plugin:${NC}"
    echo -e "   - Đăng nhập Dify Admin Panel"
    echo -e "   - Vào phần \"Plugins\" → \"Install Plugin\""
    echo -e "   - Upload file: ${PLUGIN_NAME}.signed.difypkg"
    echo -e "   - Cấu hình API Token và Account ID"
    echo -e "\n${YELLOW}3. Cấu hình Cloudflare Workers AI:${NC}"
    echo -e "   - API Token: Lấy từ Cloudflare Dashboard"
    echo -e "   - Account ID: Tìm trong Cloudflare Dashboard"
    echo -e "   - Model: @cf/qwen/qwq-32b hoặc model khác"
    echo -e "\n${GREEN}✨ Plugin sẵn sàng sử dụng!${NC}"
}

# Hàm main
main() {
    show_banner
    echo -e "${YELLOW}Plugin: ${PLUGIN_NAME} v${PLUGIN_VERSION}${NC}\n"
    
    validate_plugin_structure
    check_os
    run_api_tests
    download_dify_cli
    generate_key_pair
    package_plugin
    sign_plugin
    verify_signature
    show_plugin_info
    show_installation_guide
    
    echo -e "\n${GREEN}🎉 HOÀN THÀNH ĐÓNG GÓI PLUGIN!${NC}"
    echo -e "${BLUE}File plugin: ${PLUGIN_NAME}.signed.difypkg${NC}"
}

# Chạy script
main
