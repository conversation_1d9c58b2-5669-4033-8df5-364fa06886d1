# AIDiBiz Cloudflare Workers AI Plugin

Dify plugin cho Cloudflare Workers AI được tái cấu trúc theo chuẩn Ollama và Dify standards.

## 🚀 Tính năng

- ✅ **LLM Models**: Hỗ trợ các mô hình ngôn ngữ lớn từ Cloudflare Workers AI
- ✅ **Text Embedding**: Hỗ trợ các mô hình embedding văn bản
- ✅ **Function Calling**: Hỗ trợ gọi hàm (có thể bật/tắt qua UI)
- ✅ **Vision Support**: Hỗ trợ xử lý hình ảnh (có thể bật/tắt qua UI)
- ✅ **Streaming**: Hỗ trợ streaming response
- ✅ **Customizable Models**: Cấu hình model động qua UI

## 📁 Cấu trúc Plugin

```
cloudflare-workers-ai/
├── _assets/                    # Icons và assets
│   ├── icon_s_en.svg          # Icon nhỏ
│   └── icon_l_en.svg          # Icon lớn
├── models/                     # Model implementations
│   ├── __init__.py
│   ├── llm/
│   │   ├── __init__.py
│   │   └── llm.py             # LLM model implementation
│   └── text_embedding/
│       ├── __init__.py
│       └── text_embedding.py  # Text embedding implementation
├── provider/
│   ├── cloudflare-workers-ai.yaml  # Provider configuration
│   └── cloudflare_workers_ai.py    # Provider implementation
├── manifest.yaml              # Plugin manifest
├── main.py                    # Plugin entry point
├── requirements.txt           # Dependencies
└── README.md                  # Documentation
```

## 🔧 Cấu hình

### Provider Level (Tùy chọn)
- **Default Account ID**: Account ID mặc định cho tất cả models

### Model Level (Bắt buộc)
- **API Token**: Cloudflare Workers AI API Token
- **Account ID**: Cloudflare Account ID (có thể ghi đè provider setting)
- **Model Name**: Tên model (ví dụ: `@cf/qwen/qwq-32b-preview`)
- **Context Size**: Kích thước ngữ cảnh (mặc định: 32768)
- **Max Tokens**: Số token tối đa (mặc định: 4096)
- **Vision Support**: Bật/tắt hỗ trợ thị giác (Yes/No)
- **Function Call Support**: Bật/tắt hỗ trợ gọi hàm (Yes/No)

## 📋 Supported Models

### LLM Models
- `@cf/qwen/qwq-32b-preview`
- `@cf/meta/llama-3.1-8b-instruct`
- `@cf/meta/llama-3.2-11b-vision-instruct`
- `@cf/mistral/mistral-7b-instruct-v0.2`
- Và nhiều models khác từ [Cloudflare Workers AI](https://developers.cloudflare.com/workers-ai/models/)

### Text Embedding Models
- `@cf/baai/bge-large-en-v1.5`
- `@cf/baai/bge-base-en-v1.5`
- `@cf/baai/bge-small-en-v1.5`
- `@cf/baai/bge-m3`

## 🛠 Installation

1. Copy plugin folder vào Dify plugins directory
2. Restart Dify service
3. Configure provider credentials trong Dify UI

## 🔗 Links

- [Cloudflare Workers AI Documentation](https://developers.cloudflare.com/workers-ai/)
- [Cloudflare Workers AI Models](https://developers.cloudflare.com/workers-ai/models/)
- [Dify Plugin Development](https://docs.dify.ai/)

## 📝 Changelog

### v0.0.1
- ✅ Tái cấu trúc theo chuẩn Ollama/Dify
- ✅ Tách riêng project folder
- ✅ Chuẩn hóa tên file models (llm.py, text_embedding.py)
- ✅ Thêm __init__.py files
- ✅ Cập nhật provider YAML configuration
- ✅ Hỗ trợ đầy đủ LLM và Text Embedding
- ✅ UI configuration cho Function Calling và Vision Support
