#!/usr/bin/env python3
"""
Test script to validate Cloudflare Workers AI plugin structure
"""
import os
import sys
import yaml

def test_plugin_structure():
    """Test if plugin structure follows Dify standards"""
    print("🔍 Testing Cloudflare Workers AI Plugin Structure...")
    
    # Check required files
    required_files = [
        "manifest.yaml",
        "main.py", 
        "requirements.txt",
        "provider/cloudflare-workers-ai.yaml",
        "provider/cloudflare_workers_ai.py",
        "models/__init__.py",
        "models/llm/__init__.py",
        "models/llm/llm.py",
        "models/text_embedding/__init__.py", 
        "models/text_embedding/text_embedding.py",
        "_assets/icon_s_en.svg",
        "_assets/icon_l_en.svg"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All required files present")
    
    # Check manifest.yaml structure
    try:
        with open("manifest.yaml", "r") as f:
            manifest = yaml.safe_load(f)
        
        required_manifest_keys = ["author", "name", "version", "type", "plugins", "resource"]
        missing_keys = [key for key in required_manifest_keys if key not in manifest]
        
        if missing_keys:
            print(f"❌ Missing manifest keys: {missing_keys}")
            return False
        else:
            print("✅ Manifest structure valid")
            
        # Check if plugin type is correct
        if manifest.get("type") != "plugin":
            print(f"❌ Invalid plugin type: {manifest.get('type')}")
            return False
            
        # Check supported model types
        if "models" in manifest.get("plugins", {}):
            print("✅ Models plugin configuration found")
        else:
            print("❌ Models plugin configuration missing")
            return False
            
    except Exception as e:
        print(f"❌ Error reading manifest.yaml: {e}")
        return False
    
    # Check provider YAML structure
    try:
        with open("provider/cloudflare-workers-ai.yaml", "r") as f:
            provider_config = yaml.safe_load(f)
        
        required_provider_keys = ["provider", "supported_model_types", "configurate_methods"]
        missing_keys = [key for key in required_provider_keys if key not in provider_config]
        
        if missing_keys:
            print(f"❌ Missing provider config keys: {missing_keys}")
            return False
        else:
            print("✅ Provider configuration valid")
            
        # Check supported model types
        supported_types = provider_config.get("supported_model_types", [])
        expected_types = ["llm", "text-embedding"]
        
        if not all(t in supported_types for t in expected_types):
            print(f"❌ Missing model types. Expected: {expected_types}, Got: {supported_types}")
            return False
        else:
            print("✅ Supported model types correct")
            
    except Exception as e:
        print(f"❌ Error reading provider config: {e}")
        return False
    
    print("\n🎉 Plugin structure validation completed successfully!")
    print("\n📋 Summary:")
    print("   ✅ File structure follows Ollama/Dify standards")
    print("   ✅ Manifest configuration is valid")
    print("   ✅ Provider configuration is valid")
    print("   ✅ Model types (LLM + Text Embedding) supported")
    print("   ✅ Customizable model configuration enabled")
    
    return True

def print_structure():
    """Print the plugin directory structure"""
    print("\n📁 Plugin Directory Structure:")
    for root, dirs, files in os.walk("."):
        # Skip hidden directories and __pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        level = root.replace(".", "").count(os.sep)
        indent = " " * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        
        subindent = " " * 2 * (level + 1)
        for file in files:
            if not file.startswith('.') and not file.endswith('.pyc'):
                print(f"{subindent}{file}")

if __name__ == "__main__":
    print("🚀 Cloudflare Workers AI Plugin Structure Test")
    print("=" * 50)
    
    # Change to plugin directory
    if os.path.exists("cloudflare-workers-ai"):
        os.chdir("cloudflare-workers-ai")
    
    print_structure()
    print("\n" + "=" * 50)
    
    success = test_plugin_structure()
    
    if success:
        print("\n✅ Plugin is ready for deployment!")
        sys.exit(0)
    else:
        print("\n❌ Plugin structure needs fixes!")
        sys.exit(1)
