import json
import logging
from collections.abc import Generator
from typing import Optional

import httpx
from dify_plugin.entities.model import (
    AIModelEntity,
    FetchFrom,
    I18nObject,
    LLMMode,
    ModelFeature,
    ModelPropertyKey,
    ModelType,
    ParameterRule,
    ParameterType,
    PriceConfig,
)
from dify_plugin.entities.model.llm import LLMMode, LLMResult, LLMResultChunk, LLMResultChunkDelta
from dify_plugin.entities.model.message import (
    AssistantPromptMessage,
    PromptMessage,
    PromptMessageContentType,
    PromptMessageTool,
    SystemPromptMessage,
    ToolPromptMessage,
    UserPromptMessage,
)
from dify_plugin.errors.model import (
    CredentialsValidateFailedError,
    InvokeAuthorizationError,
    InvokeBadRequestError,
    InvokeConnectionError,
    InvokeError,
    InvokeRateLimitError,
    InvokeServerUnavailableError,
)
from dify_plugin.interfaces.model.large_language_model import LargeLanguageModel

logger = logging.getLogger(__name__)


class CloudflareWorkersAILanguageModel(LargeLanguageModel):
    def _invoke(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: list[PromptMessageTool] | None = None,
        stop: list[str] | None = None,
        stream: bool = True,
        user: str | None = None,
    ) -> LLMResult | Generator:
        return self._generate(
            model=model,
            credentials=credentials,
            prompt_messages=prompt_messages,
            model_parameters=model_parameters,
            tools=tools,
            stop=stop,
            stream=stream,
            user=user,
        )

    def get_num_tokens(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        tools: list[PromptMessageTool] | None = None,
    ) -> int:
        return self._num_tokens_from_messages(prompt_messages, tools=tools)

    def get_model_mode(self, model: str, credentials: dict) -> LLMMode:
        """
        Get model mode (always CHAT for Cloudflare Workers AI)

        :param model: model name
        :param credentials: model credentials
        :return: model mode
        """
        # Cloudflare Workers AI models are always chat completion models
        return LLMMode.CHAT

    def get_customizable_model_schema(self, model: str, credentials: dict) -> AIModelEntity:
        """
        Get customizable model schema for dynamic model configuration

        :param model: model name
        :param credentials: model credentials
        :return: model schema
        """
        from decimal import Decimal
        from typing import Any

        # Determine model features based on credentials
        extras: dict[str, Any] = {"features": []}

        # Vision support
        if "vision_support" in credentials and credentials["vision_support"] == "true":
            extras["features"].append(ModelFeature.VISION)

        # Function calling support
        if "function_call_support" in credentials and credentials["function_call_support"] == "true":
            extras["features"].append(ModelFeature.TOOL_CALL)
            extras["features"].append(ModelFeature.MULTI_TOOL_CALL)

        # Create model entity
        entity = AIModelEntity(
            model=model,
            label=I18nObject(zh_Hans=model, en_US=model),
            model_type=ModelType.LLM,
            fetch_from=FetchFrom.CUSTOMIZABLE_MODEL,
            model_properties={
                ModelPropertyKey.MODE: LLMMode.CHAT.value,
                ModelPropertyKey.CONTEXT_SIZE: int(credentials.get("context_size", 32768)),
                ModelPropertyKey.MAX_CHUNKS: 1,
            },
            parameter_rules=[
                ParameterRule(
                    name=DefaultParameterName.TEMPERATURE.value,
                    use_template=DefaultParameterName.TEMPERATURE.value,
                    label=I18nObject(en_US="Temperature", zh_Hans="温度"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Controls randomness in the model's responses. Higher values make output more creative but less focused.",
                        zh_Hans="控制模型响应的随机性。较高的值使输出更具创造性但不太集中。",
                    ),
                    default=0.7,
                    min=0.0,
                    max=2.0,
                ),
                ParameterRule(
                    name=DefaultParameterName.TOP_P.value,
                    use_template=DefaultParameterName.TOP_P.value,
                    label=I18nObject(en_US="Top P", zh_Hans="Top P"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Controls diversity via nucleus sampling. Lower values make responses more focused.",
                        zh_Hans="通过核采样控制多样性。较低的值使响应更集中。",
                    ),
                    default=0.9,
                    min=0.0,
                    max=1.0,
                ),
                ParameterRule(
                    name=DefaultParameterName.MAX_TOKENS.value,
                    use_template=DefaultParameterName.MAX_TOKENS.value,
                    label=I18nObject(en_US="Max Tokens", zh_Hans="最大令牌数"),
                    type=ParameterType.INT,
                    help=I18nObject(
                        en_US="Maximum number of tokens to generate in the response.",
                        zh_Hans="响应中生成的最大令牌数。",
                    ),
                    default=1024,
                    min=1,
                    max=int(credentials.get("max_tokens", 32768)),
                ),
                ParameterRule(
                    name=DefaultParameterName.PRESENCE_PENALTY.value,
                    use_template=DefaultParameterName.PRESENCE_PENALTY.value,
                    label=I18nObject(en_US="Presence Penalty", zh_Hans="存在惩罚"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Penalizes new tokens based on whether they appear in the text so far.",
                        zh_Hans="根据新令牌是否出现在迄今为止的文本中来惩罚它们。",
                    ),
                    default=0.0,
                    min=-2.0,
                    max=2.0,
                ),
                ParameterRule(
                    name=DefaultParameterName.FREQUENCY_PENALTY.value,
                    use_template=DefaultParameterName.FREQUENCY_PENALTY.value,
                    label=I18nObject(en_US="Frequency Penalty", zh_Hans="频率惩罚"),
                    type=ParameterType.FLOAT,
                    help=I18nObject(
                        en_US="Penalizes new tokens based on their frequency in the text so far.",
                        zh_Hans="根据新令牌在迄今为止文本中的频率来惩罚它们。",
                    ),
                    default=0.0,
                    min=-2.0,
                    max=2.0,
                ),
            ],
            pricing=PriceConfig(
                input=Decimal(credentials.get("input_price", 0)),
                output=Decimal(credentials.get("output_price", 0)),
                unit=Decimal(credentials.get("unit", 1000)),
                currency=credentials.get("currency", "USD"),
            ),
            **extras,
        )
        return entity

    def _num_tokens_from_messages(self, messages: list[PromptMessage], tools: list[PromptMessageTool] = None) -> int:
        """
        Calculate num tokens for messages
        """
        # Simple approximation - count words
        total_tokens = 0
        for message in messages:
            if hasattr(message, 'content') and message.content:
                if isinstance(message.content, str):
                    total_tokens += len(message.content.split())
                elif isinstance(message.content, list):
                    for content_item in message.content:
                        if hasattr(content_item, 'data') and isinstance(content_item.data, str):
                            total_tokens += len(content_item.data.split())

        # Add tokens for tools
        if tools:
            for tool in tools:
                total_tokens += len(tool.name.split()) if tool.name else 0
                total_tokens += len(tool.description.split()) if tool.description else 0

        return total_tokens

    def validate_credentials(self, model: str, credentials: dict) -> None:
        """
        Validate model credentials by making a test API call
        """
        # Get credentials from model configuration
        api_token = credentials.get("api_token")
        account_id = credentials.get("account_id")

        # Fallback to provider credentials if not set in model
        if not account_id:
            account_id = credentials.get("default_account_id")

        # Validate required credentials
        if not api_token:
            raise CredentialsValidateFailedError("API token is required for this model")
        if not account_id:
            raise CredentialsValidateFailedError("Account ID is required (set in model or provider configuration)")
        if not model:
            raise CredentialsValidateFailedError("Model name is required")

        try:
            # Test with a simple prompt using minimal parameters and shorter timeout for validation
            import os
            original_timeout = os.environ.get("REQUEST_TIMEOUT")
            os.environ["REQUEST_TIMEOUT"] = "30"  # 30 seconds for validation

            try:
                result = self._generate(
                    model=model,
                    credentials=credentials,
                    prompt_messages=[UserPromptMessage(content="Hi")],
                    model_parameters={"max_tokens": 5, "temperature": 0.1},
                    stream=False,
                )

                # Check if we got a valid response
                if not result or not hasattr(result, 'message') or not result.message:
                    raise CredentialsValidateFailedError("Invalid response from model - check model name and credentials")
            finally:
                # Restore original timeout
                if original_timeout:
                    os.environ["REQUEST_TIMEOUT"] = original_timeout
                elif "REQUEST_TIMEOUT" in os.environ:
                    del os.environ["REQUEST_TIMEOUT"]

        except CredentialsValidateFailedError:
            # Re-raise credential validation errors as-is
            raise
        except Exception as ex:
            # Transform other errors to credential validation errors
            error_msg = str(ex)
            if "401" in error_msg or "unauthorized" in error_msg.lower():
                raise CredentialsValidateFailedError("Invalid API token - check your Cloudflare Workers AI API token")
            elif "403" in error_msg or "forbidden" in error_msg.lower():
                raise CredentialsValidateFailedError("Access forbidden - check your account permissions")
            elif "404" in error_msg or "not found" in error_msg.lower():
                raise CredentialsValidateFailedError(f"Model '{model}' not found - check the model name")
            elif "400" in error_msg or "bad request" in error_msg.lower():
                raise CredentialsValidateFailedError(f"Bad request - check model name and account ID: {error_msg}")
            else:
                raise CredentialsValidateFailedError(f"Failed to validate credentials: {error_msg}")

    def get_customizable_model_schema(
        self, model: str, credentials: dict
    ) -> Optional[AIModelEntity]:
        """
        Get customizable model schema following Ollama/LocalAI patterns
        """
        import os
        from decimal import Decimal
        from typing import Any

        # Get configuration from credentials (user input) or environment variables
        context_size = int(credentials.get("context_size", os.getenv("CONTEXT_SIZE", "32768")))
        max_tokens_default = int(credentials.get("max_tokens", os.getenv("MAX_TOKENS", "4096")))

        # Build parameter rules
        rules = [
            ParameterRule(
                name="temperature",
                type=ParameterType.FLOAT,
                use_template="temperature",
                label=I18nObject(en_US="Temperature", vi_VN="Nhiệt độ"),
                required=False,
                default=0.7,
                min=0.0,
                max=2.0,
                precision=1,
            ),
            ParameterRule(
                name="top_p",
                type=ParameterType.FLOAT,
                use_template="top_p",
                label=I18nObject(en_US="Top P", vi_VN="Top P"),
                required=False,
                default=0.9,
                min=0.0,
                max=1.0,
                precision=2,
            ),
            ParameterRule(
                name="max_tokens",
                type=ParameterType.INT,
                use_template="max_tokens",
                label=I18nObject(en_US="Max Tokens", vi_VN="Số token tối đa"),
                required=False,
                default=max_tokens_default,
                min=1,
                max=max_tokens_default,
            ),
        ]

        # Build extras with features (following Ollama standard)
        extras: dict[str, Any] = {"features": []}

        # Process Yes/No radio buttons (using 'true'/'false' like Ollama)
        vision_support_ui = credentials.get("vision_support", "false")
        function_call_support_ui = credentials.get("function_call_support", "false")

        # Parse features from UI selection using ModelFeature
        if vision_support_ui == "true":
            extras["features"].append(ModelFeature.VISION)
        if function_call_support_ui == "true":
            extras["features"].append(ModelFeature.TOOL_CALL)

        # Build model properties based on user configuration
        # Only use supported ModelPropertyKey attributes (following LocalAI standard)
        model_properties = {
            ModelPropertyKey.MODE: LLMMode.CHAT.value,
            ModelPropertyKey.CONTEXT_SIZE: context_size,
        }

        # Note: Function calling and vision support are handled in the logic code,
        # not declared in model_properties (following LocalAI plugin standard)

        entity = AIModelEntity(
            model=model,
            label=I18nObject(en_US=model),
            fetch_from=FetchFrom.CUSTOMIZABLE_MODEL,
            model_type=ModelType.LLM,
            model_properties=model_properties,
            parameter_rules=rules,
            **extras,  # Add features using extras (following Ollama standard)
        )
        return entity

    def _generate(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: list[PromptMessageTool] | None = None,
        stop: list[str] | None = None,
        stream: bool = True,
        user: str | None = None,
    ) -> LLMResult | Generator:
        """
        Generate response using Cloudflare Workers AI API
        """
        import os

        # Get credentials from model configuration
        api_token = credentials.get("api_token")
        account_id = credentials.get("account_id")

        # Fallback to provider credentials if not set in model
        if not account_id:
            account_id = credentials.get("default_account_id")

        if not api_token:
            raise CredentialsValidateFailedError("API token is required for this model")
        if not account_id:
            raise CredentialsValidateFailedError("Account ID is required (set in model or provider configuration)")

        # Build API URL
        base_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/ai/run/{model}"

        # Convert messages to Cloudflare format
        messages = self._convert_messages(prompt_messages)

        # Build request payload
        payload = {
            "messages": messages,
            "stream": stream,
        }

        # Add model parameters
        if "temperature" in model_parameters:
            payload["temperature"] = model_parameters["temperature"]
        if "top_p" in model_parameters:
            payload["top_p"] = model_parameters["top_p"]
        if "max_tokens" in model_parameters:
            payload["max_tokens"] = model_parameters["max_tokens"]
        if stop:
            payload["stop"] = stop

        # Add tools if function calling is supported
        function_call_support = credentials.get("function_call_support", "false") == "true"
        if tools and function_call_support:
            payload["tools"] = [self._convert_tool(tool) for tool in tools]

        headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }

        # Enhanced timeout configuration (following FPT Cloud pattern)
        request_timeout = float(os.getenv("REQUEST_TIMEOUT", "300"))  # 5 minutes default
        connect_timeout = float(os.getenv("CONNECT_TIMEOUT", "10"))   # 10 seconds connect
        read_timeout = float(os.getenv("READ_TIMEOUT", "300"))        # 5 minutes read

        try:
            if stream:
                return self._handle_stream_response(
                    base_url, headers, payload, model, credentials, prompt_messages, tools,
                    request_timeout, connect_timeout, read_timeout
                )
            else:
                return self._handle_non_stream_response(
                    base_url, headers, payload, model, credentials, prompt_messages, tools,
                    request_timeout, connect_timeout, read_timeout
                )
        except Exception as ex:
            raise self._transform_invoke_error(ex)

    def _convert_messages(self, messages: list[PromptMessage]) -> list[dict]:
        """Convert Dify messages to Cloudflare Workers AI format"""
        converted_messages = []

        for message in messages:
            if isinstance(message, SystemPromptMessage):
                converted_messages.append({
                    "role": "system",
                    "content": message.content
                })
            elif isinstance(message, UserPromptMessage):
                if isinstance(message.content, str):
                    converted_messages.append({
                        "role": "user",
                        "content": message.content
                    })
                else:
                    # Handle multimodal content
                    content_parts = []
                    for content in message.content:
                        if content.type == PromptMessageContentType.TEXT:
                            content_parts.append({
                                "type": "text",
                                "text": content.data
                            })
                        elif content.type == PromptMessageContentType.IMAGE:
                            content_parts.append({
                                "type": "image_url",
                                "image_url": {"url": content.data}
                            })
                    converted_messages.append({
                        "role": "user",
                        "content": content_parts
                    })
            elif isinstance(message, AssistantPromptMessage):
                msg = {"role": "assistant", "content": message.content or ""}
                if message.tool_calls:
                    msg["tool_calls"] = [
                        {
                            "id": tool_call.id,
                            "type": "function",
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments
                            }
                        }
                        for tool_call in message.tool_calls
                    ]
                converted_messages.append(msg)
            elif isinstance(message, ToolPromptMessage):
                converted_messages.append({
                    "role": "tool",
                    "content": message.content,
                    "tool_call_id": message.tool_call_id
                })

        return converted_messages

    def _convert_tool(self, tool: PromptMessageTool) -> dict:
        """Convert Dify tool to Cloudflare Workers AI format"""
        return {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.parameters
            }
        }

    def _handle_non_stream_response(
        self,
        url: str,
        headers: dict,
        payload: dict,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        tools: list[PromptMessageTool] | None = None,
        request_timeout: float = 300.0,
        connect_timeout: float = 10.0,
        read_timeout: float = 300.0
    ) -> LLMResult:
        """Handle non-streaming response with enhanced timeout configuration"""
        # Create timeout configuration similar to FPT Cloud
        timeout_config = httpx.Timeout(
            timeout=request_timeout,
            connect=connect_timeout,
            read=read_timeout,
            write=30.0,  # Write timeout
            pool=10.0    # Pool timeout
        )

        # Retry configuration for 504 Gateway Timeout
        max_retries = 3
        retry_delay = 2.0

        for attempt in range(max_retries):
            try:
                with httpx.Client(timeout=timeout_config) as client:
                    response = client.post(url, headers=headers, json=payload)
                    response.raise_for_status()

                    result = response.json()

                    # Extract response content
                    if "result" in result and "response" in result["result"]:
                        content = result["result"]["response"]
                    else:
                        content = ""

                    # Create assistant message
                    assistant_message = AssistantPromptMessage(content=content)

                    # Calculate usage
                    prompt_tokens = self._num_tokens_from_messages(prompt_messages, tools=tools or [])
                    completion_tokens = self._num_tokens_from_messages([assistant_message], tools=[])
                    usage = self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=prompt_tokens,
                        completion_tokens=completion_tokens,
                    )

                    return LLMResult(
                        model=model,
                        prompt_messages=prompt_messages,
                        message=assistant_message,
                        usage=usage,
                    )

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 504 and attempt < max_retries - 1:
                    # Retry on 504 Gateway Timeout
                    import time
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                else:
                    raise
            except (httpx.TimeoutException, httpx.ConnectError) as e:
                if attempt < max_retries - 1:
                    # Retry on timeout/connection errors
                    import time
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                else:
                    raise

    def _handle_stream_response(
        self,
        url: str,
        headers: dict,
        payload: dict,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        tools: list[PromptMessageTool] | None = None,
        request_timeout: float = 300.0,
        connect_timeout: float = 10.0,
        read_timeout: float = 300.0
    ) -> Generator:
        """Handle streaming response with enhanced timeout configuration"""
        # Create timeout configuration similar to FPT Cloud
        timeout_config = httpx.Timeout(
            timeout=request_timeout,
            connect=connect_timeout,
            read=read_timeout,
            write=30.0,  # Write timeout
            pool=10.0    # Pool timeout
        )

        # Retry configuration for 504 Gateway Timeout
        max_retries = 3
        retry_delay = 2.0

        for attempt in range(max_retries):
            try:
                with httpx.stream("POST", url, headers=headers, json=payload, timeout=timeout_config) as response:
                    response.raise_for_status()

                    full_content = ""

                    for line in response.iter_lines():
                        if not line.strip():
                            continue

                        if line.startswith("data: "):
                            data = line[6:]  # Remove "data: " prefix

                            if data.strip() == "[DONE]":
                                break

                            try:
                                chunk_data = json.loads(data)

                                if "result" in chunk_data and "response" in chunk_data["result"]:
                                    content = chunk_data["result"]["response"]
                                    full_content += content

                                    yield LLMResultChunk(
                                        model=model,
                                        prompt_messages=prompt_messages,
                                        delta=LLMResultChunkDelta(
                                            index=0,
                                            message=AssistantPromptMessage(content=content),
                                        ),
                                    )
                            except json.JSONDecodeError:
                                continue

                    # Final usage calculation
                    prompt_tokens = self._num_tokens_from_messages(prompt_messages, tools=tools or [])
                    completion_tokens = self._num_tokens_from_messages([AssistantPromptMessage(content=full_content)], tools=[])
                    usage = self._calc_response_usage(
                        model=model,
                        credentials=credentials,
                        prompt_tokens=prompt_tokens,
                        completion_tokens=completion_tokens,
                    )

                    # Final chunk with usage
                    yield LLMResultChunk(
                        model=model,
                        prompt_messages=prompt_messages,
                        delta=LLMResultChunkDelta(
                            index=0,
                            message=AssistantPromptMessage(content=""),
                            usage=usage,
                        ),
                    )
                    return  # Success, exit retry loop

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 504 and attempt < max_retries - 1:
                    # Retry on 504 Gateway Timeout
                    import time
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                else:
                    raise
            except (httpx.TimeoutException, httpx.ConnectError) as e:
                if attempt < max_retries - 1:
                    # Retry on timeout/connection errors
                    import time
                    time.sleep(retry_delay * (attempt + 1))
                    continue
                else:
                    raise

    def _extract_response_tool_call(self, response_tool_call: dict) -> AssistantPromptMessage.ToolCall:
        """
        Extract response tool call from API response

        :param response_tool_call: tool call dict from API response
        :return: AssistantPromptMessage.ToolCall object
        """
        import json

        tool_call = None
        if response_tool_call and "function" in response_tool_call:
            arguments = response_tool_call.get("function", {}).get("arguments")
            if isinstance(arguments, dict):
                arguments = json.dumps(arguments)
            function = AssistantPromptMessage.ToolCall.ToolCallFunction(
                name=response_tool_call.get("function", {}).get("name"),
                arguments=arguments,
            )
            tool_call = AssistantPromptMessage.ToolCall(
                id=response_tool_call.get("id", response_tool_call.get("function", {}).get("name")),
                type="function",
                function=function,
            )
        return tool_call

    @property
    def _invoke_error_mapping(self) -> dict[type[InvokeError], list[type[Exception]]]:
        """
        Map model invocation errors to unified error types
        The key is the error type thrown to the caller
        The value is the error type thrown by the model, which needs to be mapped to a unified Dify error
        """
        return {
            InvokeConnectionError: [
                httpx.ConnectError,
                httpx.TimeoutException,
            ],
            InvokeServerUnavailableError: [
                httpx.HTTPStatusError,  # For 5xx errors
            ],
            InvokeRateLimitError: [
                httpx.HTTPStatusError,  # For 429 errors
            ],
            InvokeAuthorizationError: [
                httpx.HTTPStatusError,  # For 401/403 errors
            ],
            InvokeBadRequestError: [
                httpx.HTTPStatusError,  # For 400 errors
            ],
        }

    def _transform_invoke_error(self, error: Exception) -> InvokeError:
        """Transform invoke error to Dify error"""
        if isinstance(error, httpx.HTTPStatusError):
            if error.response.status_code == 401:
                return InvokeAuthorizationError("Invalid API token")
            elif error.response.status_code == 400:
                return InvokeBadRequestError(str(error))
            elif error.response.status_code == 429:
                return InvokeRateLimitError("Rate limit exceeded")
            elif error.response.status_code >= 500:
                return InvokeServerUnavailableError("Server unavailable")
            else:
                return InvokeError(str(error))
        elif isinstance(error, httpx.ConnectError):
            return InvokeConnectionError("Connection failed")
        elif isinstance(error, httpx.TimeoutException):
            return InvokeConnectionError("Request timeout")
        else:
            return InvokeError(str(error))
