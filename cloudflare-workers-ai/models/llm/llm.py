import json
import logging
from collections.abc import Generator
from typing import Optional

import httpx
from dify_plugin.entities.model import (
    AIModelEntity,
    FetchFrom,
    I18nObject,
    LLMMode,
    ModelFeature,
    ModelPropertyKey,
    ModelType,
    ParameterRule,
    ParameterType,
    PriceConfig,
)
from dify_plugin.entities.model.llm import LLMResult, LLMResultChunk, LLMResultChunkDelta
from dify_plugin.entities.model.message import (
    AssistantPromptMessage,
    PromptMessage,
    PromptMessageContentType,
    PromptMessageTool,
    SystemPromptMessage,
    ToolPromptMessage,
    UserPromptMessage,
)
from dify_plugin.errors.model import (
    CredentialsValidateFailedError,
    InvokeAuthorizationError,
    InvokeBadRequestError,
    InvokeConnectionError,
    InvokeError,
    InvokeRateLimitError,
    InvokeServerUnavailableError,
)
from dify_plugin.interfaces.model.large_language_model import LargeLanguageModel

logger = logging.getLogger(__name__)


class CloudflareWorkersAILanguageModel(LargeLanguageModel):
    def _invoke(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: list[PromptMessageTool] | None = None,
        stop: list[str] | None = None,
        stream: bool = True,
        user: str | None = None,
    ) -> LLMResult | Generator:
        return self._generate(
            model=model,
            credentials=credentials,
            prompt_messages=prompt_messages,
            model_parameters=model_parameters,
            tools=tools,
            stop=stop,
            stream=stream,
            user=user,
        )

    def get_num_tokens(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        tools: list[PromptMessageTool] | None = None,
    ) -> int:
        return self._num_tokens_from_messages(prompt_messages, tools=tools)

    def validate_credentials(self, model: str, credentials: dict) -> None:
        """
        Validate model credentials by making a test API call
        """
        # Get credentials from model configuration
        api_token = credentials.get("api_token")
        account_id = credentials.get("account_id")

        # Fallback to provider credentials if not set in model
        if not account_id:
            account_id = credentials.get("default_account_id")

        # Validate required credentials
        if not api_token:
            raise CredentialsValidateFailedError("API token is required for this model")
        if not account_id:
            raise CredentialsValidateFailedError("Account ID is required (set in model or provider configuration)")
        if not model:
            raise CredentialsValidateFailedError("Model name is required")

        try:
            # Test with a simple prompt using minimal parameters
            result = self._generate(
                model=model,
                credentials=credentials,
                prompt_messages=[UserPromptMessage(content="Hi")],
                model_parameters={"max_tokens": 5, "temperature": 0.1},
                stream=False,
            )

            # Check if we got a valid response
            if not result or not hasattr(result, 'message') or not result.message:
                raise CredentialsValidateFailedError("Invalid response from model - check model name and credentials")

        except CredentialsValidateFailedError:
            # Re-raise credential validation errors as-is
            raise
        except Exception as ex:
            # Transform other errors to credential validation errors
            error_msg = str(ex)
            if "401" in error_msg or "unauthorized" in error_msg.lower():
                raise CredentialsValidateFailedError("Invalid API token - check your Cloudflare Workers AI API token")
            elif "403" in error_msg or "forbidden" in error_msg.lower():
                raise CredentialsValidateFailedError("Access forbidden - check your account permissions")
            elif "404" in error_msg or "not found" in error_msg.lower():
                raise CredentialsValidateFailedError(f"Model '{model}' not found - check the model name")
            elif "400" in error_msg or "bad request" in error_msg.lower():
                raise CredentialsValidateFailedError(f"Bad request - check model name and account ID: {error_msg}")
            else:
                raise CredentialsValidateFailedError(f"Failed to validate credentials: {error_msg}")

    def get_customizable_model_schema(
        self, model: str, credentials: dict
    ) -> Optional[AIModelEntity]:
        """
        Get customizable model schema following Ollama/LocalAI patterns
        """
        import os
        from decimal import Decimal
        from typing import Any

        # Get configuration from credentials (user input) or environment variables
        context_size = int(credentials.get("context_size", os.getenv("CONTEXT_SIZE", "32768")))
        max_tokens_default = int(credentials.get("max_tokens", os.getenv("MAX_TOKENS", "4096")))

        # Build parameter rules
        rules = [
            ParameterRule(
                name="temperature",
                type=ParameterType.FLOAT,
                use_template="temperature",
                label=I18nObject(en_US="Temperature", vi_VN="Nhiệt độ"),
                required=False,
                default=0.7,
                min=0.0,
                max=2.0,
                precision=1,
            ),
            ParameterRule(
                name="top_p",
                type=ParameterType.FLOAT,
                use_template="top_p",
                label=I18nObject(en_US="Top P", vi_VN="Top P"),
                required=False,
                default=0.9,
                min=0.0,
                max=1.0,
                precision=2,
            ),
            ParameterRule(
                name="max_tokens",
                type=ParameterType.INT,
                use_template="max_tokens",
                label=I18nObject(en_US="Max Tokens", vi_VN="Số token tối đa"),
                required=False,
                default=max_tokens_default,
                min=1,
                max=max_tokens_default,
            ),
        ]

        # Build extras with features (following Ollama standard)
        extras: dict[str, Any] = {"features": []}

        # Process Yes/No radio buttons (using 'true'/'false' like Ollama)
        vision_support_ui = credentials.get("vision_support", "false")
        function_call_support_ui = credentials.get("function_call_support", "false")

        # Parse features from UI selection using ModelFeature
        if vision_support_ui == "true":
            extras["features"].append(ModelFeature.VISION)
        if function_call_support_ui == "true":
            extras["features"].append(ModelFeature.TOOL_CALL)

        # Build model properties based on user configuration
        # Only use supported ModelPropertyKey attributes (following LocalAI standard)
        model_properties = {
            ModelPropertyKey.MODE: LLMMode.CHAT.value,
            ModelPropertyKey.CONTEXT_SIZE: context_size,
        }

        # Note: Function calling and vision support are handled in the logic code,
        # not declared in model_properties (following LocalAI plugin standard)

        entity = AIModelEntity(
            model=model,
            label=I18nObject(en_US=model),
            fetch_from=FetchFrom.CUSTOMIZABLE_MODEL,
            model_type=ModelType.LLM,
            model_properties=model_properties,
            parameter_rules=rules,
            **extras,  # Add features using extras (following Ollama standard)
        )
        return entity

    def _generate(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: list[PromptMessageTool] | None = None,
        stop: list[str] | None = None,
        stream: bool = True,
        user: str | None = None,
    ) -> LLMResult | Generator:
        """
        Generate response using Cloudflare Workers AI API
        """
        import os

        # Get credentials from model configuration
        api_token = credentials.get("api_token")
        account_id = credentials.get("account_id")

        # Fallback to provider credentials if not set in model
        if not account_id:
            account_id = credentials.get("default_account_id")

        if not api_token:
            raise CredentialsValidateFailedError("API token is required for this model")
        if not account_id:
            raise CredentialsValidateFailedError("Account ID is required (set in model or provider configuration)")

        # Build API URL
        base_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/ai/run/{model}"

        # Convert messages to Cloudflare format
        messages = self._convert_messages(prompt_messages)

        # Build request payload
        payload = {
            "messages": messages,
            "stream": stream,
        }

        # Add model parameters
        if "temperature" in model_parameters:
            payload["temperature"] = model_parameters["temperature"]
        if "top_p" in model_parameters:
            payload["top_p"] = model_parameters["top_p"]
        if "max_tokens" in model_parameters:
            payload["max_tokens"] = model_parameters["max_tokens"]
        if stop:
            payload["stop"] = stop

        # Add tools if function calling is supported
        function_call_support = credentials.get("function_call_support", "false") == "true"
        if tools and function_call_support:
            payload["tools"] = [self._convert_tool(tool) for tool in tools]

        headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }

        try:
            if stream:
                return self._handle_stream_response(base_url, headers, payload, model, credentials, prompt_messages, tools)
            else:
                return self._handle_non_stream_response(base_url, headers, payload, model, credentials, prompt_messages, tools)
        except Exception as ex:
            raise self._transform_invoke_error(ex)

    def _convert_messages(self, messages: list[PromptMessage]) -> list[dict]:
        """Convert Dify messages to Cloudflare Workers AI format"""
        converted_messages = []

        for message in messages:
            if isinstance(message, SystemPromptMessage):
                converted_messages.append({
                    "role": "system",
                    "content": message.content
                })
            elif isinstance(message, UserPromptMessage):
                if isinstance(message.content, str):
                    converted_messages.append({
                        "role": "user",
                        "content": message.content
                    })
                else:
                    # Handle multimodal content
                    content_parts = []
                    for content in message.content:
                        if content.type == PromptMessageContentType.TEXT:
                            content_parts.append({
                                "type": "text",
                                "text": content.data
                            })
                        elif content.type == PromptMessageContentType.IMAGE:
                            content_parts.append({
                                "type": "image_url",
                                "image_url": {"url": content.data}
                            })
                    converted_messages.append({
                        "role": "user",
                        "content": content_parts
                    })
            elif isinstance(message, AssistantPromptMessage):
                msg = {"role": "assistant", "content": message.content or ""}
                if message.tool_calls:
                    msg["tool_calls"] = [
                        {
                            "id": tool_call.id,
                            "type": "function",
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments
                            }
                        }
                        for tool_call in message.tool_calls
                    ]
                converted_messages.append(msg)
            elif isinstance(message, ToolPromptMessage):
                converted_messages.append({
                    "role": "tool",
                    "content": message.content,
                    "tool_call_id": message.tool_call_id
                })

        return converted_messages

    def _convert_tool(self, tool: PromptMessageTool) -> dict:
        """Convert Dify tool to Cloudflare Workers AI format"""
        return {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.parameters
            }
        }

    def _handle_non_stream_response(
        self,
        url: str,
        headers: dict,
        payload: dict,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        tools: list[PromptMessageTool] | None = None
    ) -> LLMResult:
        """Handle non-streaming response"""
        with httpx.Client() as client:
            response = client.post(url, headers=headers, json=payload, timeout=120.0)
            response.raise_for_status()

            result = response.json()

            # Extract response content
            if "result" in result and "response" in result["result"]:
                content = result["result"]["response"]
            else:
                content = ""

            # Create assistant message
            assistant_message = AssistantPromptMessage(content=content)

            # Calculate usage
            prompt_tokens = self._num_tokens_from_messages(prompt_messages, tools=tools or [])
            completion_tokens = self._num_tokens_from_messages([assistant_message], tools=[])
            usage = self._calc_response_usage(
                model=model,
                credentials=credentials,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
            )

            return LLMResult(
                model=model,
                prompt_messages=prompt_messages,
                message=assistant_message,
                usage=usage,
            )

    def _handle_stream_response(
        self,
        url: str,
        headers: dict,
        payload: dict,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        tools: list[PromptMessageTool] | None = None
    ) -> Generator:
        """Handle streaming response"""
        with httpx.stream("POST", url, headers=headers, json=payload, timeout=120.0) as response:
            response.raise_for_status()

            full_content = ""

            for line in response.iter_lines():
                if not line.strip():
                    continue

                if line.startswith("data: "):
                    data = line[6:]  # Remove "data: " prefix

                    if data.strip() == "[DONE]":
                        break

                    try:
                        chunk_data = json.loads(data)

                        if "result" in chunk_data and "response" in chunk_data["result"]:
                            content = chunk_data["result"]["response"]
                            full_content += content

                            yield LLMResultChunk(
                                model=model,
                                prompt_messages=prompt_messages,
                                delta=LLMResultChunkDelta(
                                    index=0,
                                    message=AssistantPromptMessage(content=content),
                                ),
                            )
                    except json.JSONDecodeError:
                        continue

            # Final usage calculation
            prompt_tokens = self._num_tokens_from_messages(prompt_messages, tools=tools or [])
            completion_tokens = self._num_tokens_from_messages([AssistantPromptMessage(content=full_content)], tools=[])
            usage = self._calc_response_usage(
                model=model,
                credentials=credentials,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
            )

            # Final chunk with usage
            yield LLMResultChunk(
                model=model,
                prompt_messages=prompt_messages,
                delta=LLMResultChunkDelta(
                    index=0,
                    message=AssistantPromptMessage(content=""),
                    usage=usage,
                ),
            )

    @property
    def _invoke_error_mapping(self) -> dict[type[InvokeError], list[type[Exception]]]:
        """
        Map model invocation errors to unified error types
        The key is the error type thrown to the caller
        The value is the error type thrown by the model, which needs to be mapped to a unified Dify error
        """
        return {
            InvokeConnectionError: [
                httpx.ConnectError,
                httpx.TimeoutException,
            ],
            InvokeServerUnavailableError: [
                httpx.HTTPStatusError,  # For 5xx errors
            ],
            InvokeRateLimitError: [
                httpx.HTTPStatusError,  # For 429 errors
            ],
            InvokeAuthorizationError: [
                httpx.HTTPStatusError,  # For 401/403 errors
            ],
            InvokeBadRequestError: [
                httpx.HTTPStatusError,  # For 400 errors
            ],
        }

    def _transform_invoke_error(self, error: Exception) -> InvokeError:
        """Transform invoke error to Dify error"""
        if isinstance(error, httpx.HTTPStatusError):
            if error.response.status_code == 401:
                return InvokeAuthorizationError("Invalid API token")
            elif error.response.status_code == 400:
                return InvokeBadRequestError(str(error))
            elif error.response.status_code == 429:
                return InvokeRateLimitError("Rate limit exceeded")
            elif error.response.status_code >= 500:
                return InvokeServerUnavailableError("Server unavailable")
            else:
                return InvokeError(str(error))
        elif isinstance(error, httpx.ConnectError):
            return InvokeConnectionError("Connection failed")
        elif isinstance(error, httpx.TimeoutException):
            return InvokeConnectionError("Request timeout")
        else:
            return InvokeError(str(error))
