#!/usr/bin/env python3
"""
Debug Dify compliance for Cloudflare Workers AI plugin in new location
"""
import sys
import os
import inspect

def test_plugin_structure():
    """Test plugin structure in current directory"""
    print("🔍 Testing Plugin Structure in Current Directory...")
    
    # Check if files exist
    llm_file = "models/llm/llm.py"
    embedding_file = "models/text_embedding/text_embedding.py"
    manifest_file = "manifest.yaml"
    
    files_to_check = [llm_file, embedding_file, manifest_file]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - NOT FOUND")
            return False
    
    return True

def test_imports_and_functions():
    """Test if we can import and check functions"""
    print("\n🔍 Testing Imports and Functions...")
    
    # Add current directory to Python path
    current_dir = os.getcwd()
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    try:
        # Try to import the LLM model
        from models.llm.llm import CloudflareWorkersAILanguageModel
        print("✅ Successfully imported CloudflareWorkersAILanguageModel")
        
        # Create instance
        llm_model = CloudflareWorkersAILanguageModel()
        print("✅ Successfully created LLM model instance")
        
        # Check required functions
        required_functions = [
            '_invoke',
            'get_num_tokens', 
            'validate_credentials',
            'get_customizable_model_schema',
            'get_model_mode',
            '_num_tokens_from_messages',
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(llm_model, func_name):
                if callable(getattr(llm_model, func_name)):
                    print(f"✅ {func_name}")
                else:
                    print(f"❌ {func_name} (not callable)")
                    missing_functions.append(func_name)
            else:
                print(f"❌ {func_name} (missing)")
                missing_functions.append(func_name)
        
        # Check _invoke_error_mapping property
        if hasattr(llm_model, '_invoke_error_mapping'):
            if isinstance(getattr(type(llm_model), '_invoke_error_mapping', None), property):
                print("✅ _invoke_error_mapping (property)")
            else:
                print("❌ _invoke_error_mapping (not property)")
                missing_functions.append('_invoke_error_mapping (not property)')
        else:
            print("❌ _invoke_error_mapping (missing)")
            missing_functions.append('_invoke_error_mapping')
        
        return len(missing_functions) == 0, missing_functions
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False, [f"Import error: {e}"]
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False, [f"Unexpected error: {e}"]

def test_function_signatures():
    """Test function signatures"""
    print("\n🔍 Testing Function Signatures...")
    
    try:
        from models.llm.llm import CloudflareWorkersAILanguageModel
        llm_model = CloudflareWorkersAILanguageModel()
        
        # Test validate_credentials signature
        validate_sig = inspect.signature(llm_model.validate_credentials)
        expected_params = ['model', 'credentials']
        actual_params = list(validate_sig.parameters.keys())[1:]  # Skip 'self'
        
        if actual_params == expected_params:
            print("✅ validate_credentials signature correct")
        else:
            print(f"❌ validate_credentials signature: expected {expected_params}, got {actual_params}")
            return False
        
        # Test get_customizable_model_schema signature
        schema_sig = inspect.signature(llm_model.get_customizable_model_schema)
        expected_params = ['model', 'credentials']
        actual_params = list(schema_sig.parameters.keys())[1:]  # Skip 'self'
        
        if actual_params == expected_params:
            print("✅ get_customizable_model_schema signature correct")
        else:
            print(f"❌ get_customizable_model_schema signature: expected {expected_params}, got {actual_params}")
            return False
        
        # Test get_model_mode signature
        mode_sig = inspect.signature(llm_model.get_model_mode)
        expected_params = ['model', 'credentials']
        actual_params = list(mode_sig.parameters.keys())[1:]  # Skip 'self'
        
        if actual_params == expected_params:
            print("✅ get_model_mode signature correct")
        else:
            print(f"❌ get_model_mode signature: expected {expected_params}, got {actual_params}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing signatures: {e}")
        return False

def test_model_mode_implementation():
    """Test get_model_mode implementation"""
    print("\n🔍 Testing get_model_mode Implementation...")
    
    try:
        from models.llm.llm import CloudflareWorkersAILanguageModel
        llm_model = CloudflareWorkersAILanguageModel()
        
        # Test get_model_mode
        result = llm_model.get_model_mode("test-model", {})
        print(f"✅ get_model_mode returned: {result}")
        
        # Check if it's LLMMode.CHAT
        from dify_plugin.entities.model.llm import LLMMode
        if result == LLMMode.CHAT:
            print("✅ get_model_mode returns LLMMode.CHAT")
            return True
        else:
            print(f"❌ get_model_mode should return LLMMode.CHAT, got {result}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing get_model_mode: {e}")
        return False

def test_credentials_validation():
    """Test credentials validation with real credentials"""
    print("\n🔍 Testing Credentials Validation...")
    
    try:
        from models.llm.llm import CloudflareWorkersAILanguageModel
        llm_model = CloudflareWorkersAILanguageModel()
        
        # Test credentials
        test_credentials = {
            "api_token": "jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd",
            "account_id": "3566bb7911a7d24dcfb1fe4589f9427b"
        }
        
        test_model = "@cf/qwen/qwq-32b"
        
        print(f"Testing validation with model: {test_model}")
        print("Testing with real credentials...")
        
        try:
            llm_model.validate_credentials(test_model, test_credentials)
            print("✅ Credentials validation PASSED")
            return True
        except Exception as e:
            print(f"❌ Credentials validation FAILED: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Error setting up validation test: {e}")
        return False

def test_model_schema():
    """Test get_customizable_model_schema"""
    print("\n🔍 Testing Model Schema Generation...")
    
    try:
        from models.llm.llm import CloudflareWorkersAILanguageModel
        llm_model = CloudflareWorkersAILanguageModel()
        
        test_credentials = {
            "context_size": "32768",
            "max_tokens": "4096",
            "vision_support": "false",
            "function_call_support": "true"
        }
        
        test_model = "@cf/qwen/qwq-32b"
        
        schema = llm_model.get_customizable_model_schema(test_model, test_credentials)
        
        if schema:
            print("✅ Model schema generated successfully")
            print(f"   Model: {schema.model}")
            print(f"   Type: {schema.model_type}")
            print(f"   Features: {getattr(schema, 'features', 'None')}")
            return True
        else:
            print("❌ Model schema is None")
            return False
        
    except Exception as e:
        print(f"❌ Error testing model schema: {e}")
        return False

def main():
    """Run all debug tests"""
    print("🚀 Cloudflare Workers AI Plugin - Debug Dify Compliance")
    print("=" * 60)
    print(f"📁 Working Directory: {os.getcwd()}")
    print("=" * 60)
    
    tests = [
        ("Plugin Structure", test_plugin_structure),
        ("Imports and Functions", lambda: test_imports_and_functions()[0]),
        ("Function Signatures", test_function_signatures),
        ("Model Mode Implementation", test_model_mode_implementation),
        ("Credentials Validation", test_credentials_validation),
        ("Model Schema", test_model_schema),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 DEBUG SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Plugin should work in Dify.")
    else:
        print("⚠️ Some tests failed. Plugin may not work properly in Dify.")
        
        # Show specific issues
        if "Imports and Functions" in results and not results["Imports and Functions"]:
            success, missing = test_imports_and_functions()
            if not success:
                print(f"\n❌ Missing functions: {missing}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
