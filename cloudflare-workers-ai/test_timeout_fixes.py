#!/usr/bin/env python3
"""
Test timeout fixes for Cloudflare Workers AI plugin
"""
import json
import httpx
import time
from typing import Dict, List, Any

# Test configuration
API_BASE_URL = "https://api.cloudflare.com/client/v4/accounts/3566bb7911a7d24dcfb1fe4589f9427b/ai/run/"
HEADERS = {"Authorization": "Bearer jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd"}
TEST_MODEL = "@cf/qwen/qwq-32b"

def test_timeout_configuration():
    """Test enhanced timeout configuration"""
    print("🔍 Testing Enhanced Timeout Configuration...")
    
    url = f"{API_BASE_URL}{TEST_MODEL}"
    payload = {
        "messages": [
            {"role": "user", "content": "Write a detailed explanation of quantum computing in 500 words"}
        ],
        "stream": False,
        "max_tokens": 1000,
        "temperature": 0.7
    }
    
    # Enhanced timeout configuration (following FPT Cloud pattern)
    timeout_config = httpx.Timeout(
        timeout=300.0,    # 5 minutes total
        connect=10.0,     # 10 seconds connect
        read=300.0,       # 5 minutes read
        write=30.0,       # 30 seconds write
        pool=10.0         # 10 seconds pool
    )
    
    try:
        start_time = time.time()
        
        with httpx.Client(timeout=timeout_config) as client:
            response = client.post(url, headers=HEADERS, json=payload)
            response.raise_for_status()
            
            result = response.json()
            end_time = time.time()
            
            print(f"✅ Status: {response.status_code}")
            print(f"⏱️ Response time: {end_time - start_time:.2f} seconds")
            
            # Extract content
            if "result" in result and "response" in result["result"]:
                content = result["result"]["response"]
                print(f"📝 Content length: {len(content)} characters")
                print(f"💬 First 100 chars: {content[:100]}...")
                return True
            else:
                print("❌ Unexpected response format")
                return False
                
    except httpx.TimeoutException as e:
        print(f"⏰ Timeout error: {e}")
        return False
    except httpx.HTTPStatusError as e:
        print(f"🚫 HTTP error: {e.response.status_code} - {e}")
        if e.response.status_code == 504:
            print("🔄 This is a 504 Gateway Timeout - will be handled by retry logic")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_retry_logic_simulation():
    """Test retry logic with simulated 504 errors"""
    print("\n🔍 Testing Retry Logic (Simulation)...")
    
    url = f"{API_BASE_URL}{TEST_MODEL}"
    payload = {
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "stream": False,
        "max_tokens": 10,
        "temperature": 0.7
    }
    
    timeout_config = httpx.Timeout(
        timeout=60.0,
        connect=10.0,
        read=60.0,
        write=30.0,
        pool=10.0
    )
    
    max_retries = 3
    retry_delay = 2.0
    
    for attempt in range(max_retries):
        try:
            print(f"🔄 Attempt {attempt + 1}/{max_retries}")
            start_time = time.time()
            
            with httpx.Client(timeout=timeout_config) as client:
                response = client.post(url, headers=HEADERS, json=payload)
                response.raise_for_status()
                
                result = response.json()
                end_time = time.time()
                
                print(f"✅ Success on attempt {attempt + 1}")
                print(f"⏱️ Response time: {end_time - start_time:.2f} seconds")
                
                if "result" in result and "response" in result["result"]:
                    content = result["result"]["response"]
                    print(f"💬 Content: {content}")
                    return True
                else:
                    print("❌ Unexpected response format")
                    return False
                    
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 504 and attempt < max_retries - 1:
                print(f"🔄 Got 504 Gateway Timeout, retrying in {retry_delay * (attempt + 1)} seconds...")
                time.sleep(retry_delay * (attempt + 1))
                continue
            else:
                print(f"❌ HTTP error: {e.response.status_code} - {e}")
                return False
        except (httpx.TimeoutException, httpx.ConnectError) as e:
            if attempt < max_retries - 1:
                print(f"🔄 Got timeout/connection error, retrying in {retry_delay * (attempt + 1)} seconds...")
                time.sleep(retry_delay * (attempt + 1))
                continue
            else:
                print(f"❌ Final timeout/connection error: {e}")
                return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
    
    print("❌ All retry attempts failed")
    return False

def test_streaming_with_timeout():
    """Test streaming with enhanced timeout"""
    print("\n🔍 Testing Streaming with Enhanced Timeout...")
    
    url = f"{API_BASE_URL}{TEST_MODEL}"
    payload = {
        "messages": [
            {"role": "user", "content": "Count from 1 to 10 slowly"}
        ],
        "stream": True,
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    timeout_config = httpx.Timeout(
        timeout=300.0,
        connect=10.0,
        read=300.0,
        write=30.0,
        pool=10.0
    )
    
    try:
        start_time = time.time()
        
        with httpx.stream("POST", url, headers=HEADERS, json=payload, timeout=timeout_config) as response:
            response.raise_for_status()
            print(f"✅ Stream started, status: {response.status_code}")
            
            full_content = ""
            chunk_count = 0
            
            for line in response.iter_lines():
                if not line.strip():
                    continue
                    
                if line.startswith("data: "):
                    data = line[6:]  # Remove "data: " prefix
                    
                    if data.strip() == "[DONE]":
                        print("🏁 Stream completed")
                        break
                    
                    try:
                        chunk_data = json.loads(data)
                        chunk_count += 1
                        
                        if "result" in chunk_data and "response" in chunk_data["result"]:
                            content = chunk_data["result"]["response"]
                            full_content += content
                            print(f"📦 Chunk {chunk_count}: {repr(content)}")
                            
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON decode error: {e}")
                        continue
            
            end_time = time.time()
            print(f"💬 Full content: {full_content}")
            print(f"📊 Total chunks: {chunk_count}")
            print(f"⏱️ Total time: {end_time - start_time:.2f} seconds")
            return True
            
    except httpx.TimeoutException as e:
        print(f"⏰ Streaming timeout error: {e}")
        return False
    except httpx.HTTPStatusError as e:
        print(f"🚫 Streaming HTTP error: {e.response.status_code} - {e}")
        return False
    except Exception as e:
        print(f"❌ Streaming error: {e}")
        return False

def test_embedding_with_timeout():
    """Test text embedding with enhanced timeout"""
    print("\n🔍 Testing Text Embedding with Enhanced Timeout...")
    
    embedding_model = "@cf/baai/bge-large-en-v1.5"
    url = f"{API_BASE_URL}{embedding_model}"
    
    payload = {
        "text": "This is a test sentence for embedding with enhanced timeout configuration."
    }
    
    timeout_config = httpx.Timeout(
        timeout=120.0,
        connect=10.0,
        read=120.0,
        write=30.0,
        pool=10.0
    )
    
    try:
        start_time = time.time()
        
        with httpx.Client(timeout=timeout_config) as client:
            response = client.post(url, headers=HEADERS, json=payload)
            response.raise_for_status()
            
            result = response.json()
            end_time = time.time()
            
            print(f"✅ Status: {response.status_code}")
            print(f"⏱️ Response time: {end_time - start_time:.2f} seconds")
            
            if "result" in result and "data" in result["result"]:
                embedding = result["result"]["data"]
                print(f"📊 Embedding dimensions: {len(embedding)}")
                print(f"🔢 First 5 values: {embedding[:5]}")
                return True
            else:
                print("❌ Unexpected response format")
                print(f"📝 Response: {json.dumps(result, indent=2)}")
                return False
                
    except httpx.TimeoutException as e:
        print(f"⏰ Embedding timeout error: {e}")
        return False
    except httpx.HTTPStatusError as e:
        print(f"🚫 Embedding HTTP error: {e.response.status_code} - {e}")
        return False
    except Exception as e:
        print(f"❌ Embedding error: {e}")
        return False

def main():
    """Run all timeout tests"""
    print("🚀 Cloudflare Workers AI - Timeout Fixes Test")
    print("=" * 60)
    print("🎯 Testing enhanced timeout configuration and retry logic")
    print("=" * 60)
    
    tests = [
        ("Enhanced Timeout Config", test_timeout_configuration),
        ("Retry Logic Simulation", test_retry_logic_simulation),
        ("Streaming with Timeout", test_streaming_with_timeout),
        ("Embedding with Timeout", test_embedding_with_timeout),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TIMEOUT FIXES TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All timeout tests passed! 504 Gateway Timeout should be resolved.")
    else:
        print("⚠️ Some tests failed. Check the timeout configuration.")
    
    print("\n🔧 Applied Fixes:")
    print("✅ Enhanced timeout configuration (5 min total, 10s connect)")
    print("✅ Retry logic for 504 Gateway Timeout (3 attempts)")
    print("✅ Separate timeouts for connect/read/write/pool")
    print("✅ Exponential backoff for retries")
    print("✅ Shorter timeout for credential validation (30s)")

if __name__ == "__main__":
    main()
