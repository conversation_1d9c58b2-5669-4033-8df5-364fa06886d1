#!/usr/bin/env python3
"""
Direct test of Cloudflare Workers AI API functions
"""
import json
import httpx
import asyncio
from typing import Dict, List, Any

# Test configuration
API_BASE_URL = "https://api.cloudflare.com/client/v4/accounts/3566bb7911a7d24dcfb1fe4589f9427b/ai/run/"
HEADERS = {"Authorization": "Bearer jlyzRzxQkrQkbxxWedHF7GjPYLoFCqjafgg7TlZd"}
TEST_MODEL = "@cf/qwen/qwq-32b"

def test_basic_chat():
    """Test basic chat completion"""
    print("🔍 Testing Basic Chat Completion...")
    
    url = f"{API_BASE_URL}{TEST_MODEL}"
    payload = {
        "messages": [
            {"role": "user", "content": "Hello! What is 2+2?"}
        ],
        "stream": False,
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        with httpx.Client() as client:
            response = client.post(url, headers=HEADERS, json=payload, timeout=60.0)
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📝 Response: {json.dumps(result, indent=2)}")
            
            # Extract content
            if "result" in result and "response" in result["result"]:
                content = result["result"]["response"]
                print(f"💬 Content: {content}")
                return True
            else:
                print("❌ Unexpected response format")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_streaming_chat():
    """Test streaming chat completion"""
    print("\n🔍 Testing Streaming Chat Completion...")
    
    url = f"{API_BASE_URL}{TEST_MODEL}"
    payload = {
        "messages": [
            {"role": "user", "content": "Count from 1 to 5 slowly"}
        ],
        "stream": True,
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        with httpx.stream("POST", url, headers=HEADERS, json=payload, timeout=60.0) as response:
            response.raise_for_status()
            print(f"✅ Status: {response.status_code}")
            
            full_content = ""
            chunk_count = 0
            
            for line in response.iter_lines():
                if not line.strip():
                    continue
                    
                if line.startswith("data: "):
                    data = line[6:]  # Remove "data: " prefix
                    
                    if data.strip() == "[DONE]":
                        print("🏁 Stream completed")
                        break
                    
                    try:
                        chunk_data = json.loads(data)
                        chunk_count += 1
                        
                        if "result" in chunk_data and "response" in chunk_data["result"]:
                            content = chunk_data["result"]["response"]
                            full_content += content
                            print(f"📦 Chunk {chunk_count}: {repr(content)}")
                            
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON decode error: {e}")
                        continue
            
            print(f"💬 Full content: {full_content}")
            print(f"📊 Total chunks: {chunk_count}")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_function_calling():
    """Test function calling capability"""
    print("\n🔍 Testing Function Calling...")
    
    url = f"{API_BASE_URL}{TEST_MODEL}"
    
    # Define a simple function
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get current weather for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city name"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    payload = {
        "messages": [
            {"role": "user", "content": "What's the weather like in Paris?"}
        ],
        "tools": tools,
        "stream": False,
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    try:
        with httpx.Client() as client:
            response = client.post(url, headers=HEADERS, json=payload, timeout=60.0)
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📝 Response: {json.dumps(result, indent=2)}")
            
            # Check if function was called
            if "result" in result:
                if "tool_calls" in str(result):
                    print("🔧 Function calling detected!")
                    return True
                else:
                    print("💬 Regular response (function calling may not be supported)")
                    return True
            else:
                print("❌ Unexpected response format")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_vision_capability():
    """Test vision capability with a simple image"""
    print("\n🔍 Testing Vision Capability...")
    
    # Use a simple base64 encoded 1x1 pixel image for testing
    test_image_base64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    url = f"{API_BASE_URL}{TEST_MODEL}"
    payload = {
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "What do you see in this image?"
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": test_image_base64}
                    }
                ]
            }
        ],
        "stream": False,
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        with httpx.Client() as client:
            response = client.post(url, headers=HEADERS, json=payload, timeout=60.0)
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📝 Response: {json.dumps(result, indent=2)}")
            
            if "result" in result and "response" in result["result"]:
                content = result["result"]["response"]
                print(f"👁️ Vision response: {content}")
                return True
            else:
                print("❌ Unexpected response format")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        print("ℹ️ Vision may not be supported by this model")
        return False

def test_embedding_model():
    """Test text embedding model"""
    print("\n🔍 Testing Text Embedding...")
    
    embedding_model = "@cf/baai/bge-large-en-v1.5"
    url = f"{API_BASE_URL}{embedding_model}"
    
    payload = {
        "text": "Hello, this is a test sentence for embedding."
    }
    
    try:
        with httpx.Client() as client:
            response = client.post(url, headers=HEADERS, json=payload, timeout=60.0)
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ Status: {response.status_code}")
            
            if "result" in result and "data" in result["result"]:
                embedding = result["result"]["data"]
                print(f"📊 Embedding dimensions: {len(embedding)}")
                print(f"🔢 First 5 values: {embedding[:5]}")
                return True
            else:
                print("❌ Unexpected response format")
                print(f"📝 Response: {json.dumps(result, indent=2)}")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Cloudflare Workers AI Direct API Test")
    print("=" * 60)
    print(f"🔗 Base URL: {API_BASE_URL}")
    print(f"🤖 Test Model: {TEST_MODEL}")
    print("=" * 60)
    
    tests = [
        ("Basic Chat", test_basic_chat),
        ("Streaming Chat", test_streaming_chat),
        ("Function Calling", test_function_calling),
        ("Vision Capability", test_vision_capability),
        ("Text Embedding", test_embedding_model),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Plugin is ready for packaging.")
    else:
        print("⚠️ Some tests failed. Please check the configuration.")

if __name__ == "__main__":
    main()
