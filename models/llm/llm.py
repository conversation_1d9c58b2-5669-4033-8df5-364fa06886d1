from collections.abc import Generator
from typing import Optional, cast
from dify_plugin.entities.model import (
    AIModelEntity,
    FetchFrom,
    I18nObject,
    ModelFeature,
    ModelPropertyKey,
    ModelType,
    ParameterRule,
    ParameterType,
)
from dify_plugin.entities.model.llm import (
    LLMMode,
    LLMResult,
    LLMResultChunk,
    LLMResultChunkDelta,
)
from dify_plugin.entities.model.message import (
    AssistantPromptMessage,
    PromptMessage,
    PromptMessageTool,
    SystemPromptMessage,
    UserPromptMessage,
)
from dify_plugin.errors.model import (
    CredentialsValidateFailedError,
    InvokeAuthorizationError,
    InvokeBadRequestError,
    InvokeConnectionError,
    InvokeError,
    InvokeRateLimitError,
    InvokeServerUnavailableError,
)
from dify_plugin.interfaces.model.large_language_model import LargeLanguageModel
from httpx import Timeout
from openai import (
    APIConnectionError,
    APITimeoutError,
    AuthenticationError,
    ConflictError,
    InternalServerError,
    NotFoundError,
    OpenAI,
    PermissionDeniedError,
    RateLimitError,
    Stream,
    UnprocessableEntityError,
)
from openai.types.chat import ChatCompletion, ChatCompletionChunk


class FPTCloudLanguageModel(LargeLanguageModel):
    def _invoke(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: list[PromptMessageTool] | None = None,
        stop: list[str] | None = None,
        stream: bool = True,
        user: str | None = None,
    ) -> LLMResult | Generator:
        return self._generate(
            model=model,
            credentials=credentials,
            prompt_messages=prompt_messages,
            model_parameters=model_parameters,
            tools=tools,
            stop=stop,
            stream=stream,
            user=user,
        )

    def get_num_tokens(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        tools: list[PromptMessageTool] | None = None,
    ) -> int:
        return self._num_tokens_from_messages(prompt_messages, tools=tools)

    def get_model_mode(self, model: str, credentials: dict) -> LLMMode:
        """
        Get model mode (always CHAT for Cloudflare Workers AI)

        :param model: model name
        :param credentials: model credentials
        :return: model mode
        """
        # Cloudflare Workers AI models are always chat completion models
        return LLMMode.CHAT

    def _num_tokens_from_messages(
        self, messages: list[PromptMessage], tools: list[PromptMessageTool]
    ) -> int:
        """
        Calculate num tokens for AIDiBiz model
        """

        def tokens(text: str):
            """
            Use gpt2 tokenizer to calculate the num tokens for convenience.
            """
            return self._get_num_tokens_by_gpt2(text)

        tokens_per_message = 3
        tokens_per_name = 1
        num_tokens = 0
        messages_dict = [self._convert_prompt_message_to_dict(m) for m in messages]
        for message in messages_dict:
            num_tokens += tokens_per_message
            for key, value in message.items():
                if isinstance(value, list):
                    text = ""
                    for item in value:
                        if isinstance(item, dict) and item["type"] == "text":
                            text += item["text"]
                    value = text
                else:
                    num_tokens += tokens(str(value))
                if key == "name":
                    num_tokens += tokens_per_name
        num_tokens += 3
        if tools:
            num_tokens += self._num_tokens_for_tools(tools)
        return num_tokens

    def _num_tokens_for_tools(self, tools: list[PromptMessageTool]) -> int:
        """
        Calculate num tokens for tool calling
        """

        def tokens(text: str):
            return self._get_num_tokens_by_gpt2(text)

        num_tokens = 0
        for tool in tools:
            num_tokens += tokens("name")
            num_tokens += tokens(tool.name)
            num_tokens += tokens("description")
            num_tokens += tokens(tool.description)
            parameters = tool.parameters
            num_tokens += tokens("parameters")
            num_tokens += tokens("type")
            num_tokens += tokens(parameters.get("type"))
            if "properties" in parameters:
                num_tokens += tokens("properties")
                for key, value in parameters.get("properties").items():
                    num_tokens += tokens(key)
                    for field_key, field_value in value.items():
                        num_tokens += tokens(field_key)
                        if field_key == "enum":
                            for enum_field in field_value:
                                num_tokens += 3
                                num_tokens += tokens(enum_field)
                        else:
                            num_tokens += tokens(field_key)
                            num_tokens += tokens(str(field_value))
            if "required" in parameters:
                num_tokens += tokens("required")
                for required_field in parameters["required"]:
                    num_tokens += 3
                    num_tokens += tokens(required_field)
        return num_tokens

    def validate_credentials(self, model: str, credentials: dict) -> None:
        """
        Validate model credentials
        """
        try:
            self._invoke(
                model=model,
                credentials=credentials,
                prompt_messages=[UserPromptMessage(content="ping")],
                model_parameters={"max_tokens": 10},
                stop=[],
                stream=False,
            )
        except Exception as ex:
            raise CredentialsValidateFailedError(f"Invalid credentials {str(ex)}")

    def get_customizable_model_schema(
        self, model: str, credentials: dict
    ) -> Optional[AIModelEntity]:
        import os
        from typing import Any

        # Get configuration from credentials (user input) or environment variables
        context_size = int(credentials.get("context_size", os.getenv("CONTEXT_SIZE", "8192")))
        max_tokens_default = int(credentials.get("max_tokens", os.getenv("MAX_TOKENS", "4096")))
        temperature_default = float(os.getenv("TEMPERATURE", "0.7"))
        top_p_default = float(os.getenv("TOP_P", "0.9"))

        # Build extras with features (following Ollama standard)
        extras: dict[str, Any] = {"features": []}

        # Get features from user UI selection (using 'true'/'false' like Ollama)
        vision_support_ui = credentials.get("vision_support", "false")
        function_call_support_ui = credentials.get("function_call_support", "false")

        # Parse features from UI selection
        if vision_support_ui == "true":
            extras["features"].append(ModelFeature.VISION)
        if function_call_support_ui == "true":
            extras["features"].append(ModelFeature.TOOL_CALL)

        rules = [
            ParameterRule(
                name="temperature",
                type=ParameterType.FLOAT,
                use_template="temperature",
                default=temperature_default,
                min=0.0,
                max=2.0,
                label=I18nObject(vi_VN="Nhiệt độ", en_US="Temperature"),
            ),
            ParameterRule(
                name="top_p",
                type=ParameterType.FLOAT,
                use_template="top_p",
                default=top_p_default,
                min=0.0,
                max=1.0,
                label=I18nObject(vi_VN="Top P", en_US="Top P"),
            ),
            ParameterRule(
                name="max_tokens",
                type=ParameterType.INT,
                use_template="max_tokens",
                min=1,
                max=max_tokens_default,
                default=min(1024, max_tokens_default),
                label=I18nObject(vi_VN="Số token tối đa", en_US="Max Tokens"),
            ),
        ]
        
        # Build model properties based on user configuration
        # Only use supported ModelPropertyKey attributes (following LocalAI standard)
        model_properties = {
            ModelPropertyKey.MODE: LLMMode.CHAT.value,
            ModelPropertyKey.CONTEXT_SIZE: context_size,
        }

        # Note: Function calling and vision support are handled in the logic code,
        # not declared in model_properties (following LocalAI plugin standard)

        entity = AIModelEntity(
            model=model,
            label=I18nObject(en_US=model),
            fetch_from=FetchFrom.CUSTOMIZABLE_MODEL,
            model_type=ModelType.LLM,
            model_properties=model_properties,
            parameter_rules=rules,
            **extras,  # Add features using extras (following Ollama standard)
        )
        return entity

    def _generate(
        self,
        model: str,
        credentials: dict,
        prompt_messages: list[PromptMessage],
        model_parameters: dict,
        tools: list[PromptMessageTool] | None = None,
        stop: list[str] | None = None,
        stream: bool = True,
        user: str | None = None,
    ) -> LLMResult | Generator:
        import os

        kwargs = self._to_client_kwargs(credentials)
        client = OpenAI(**kwargs)
        model_name = model
        request_timeout = float(os.getenv("REQUEST_TIMEOUT", "120"))
        extra_model_kwargs = {"timeout": request_timeout}
        if stop:
            extra_model_kwargs["stop"] = stop
        if user:
            extra_model_kwargs["user"] = user

        # Check if model supports function calling and user enabled it
        function_call_support_ui = credentials.get("function_call_support", "false")
        model_supports_function_calling = self._model_supports_function_calling(model)

        # FINAL CONCLUSION: FPT Cloud API does NOT support automatic function calling
        # - Any tools parameter triggers "auto" tool choice by default
        # - "auto" tool choice requires server-side configuration not available
        # - Only forced function calls work, but Dify needs automatic decision making
        # - Therefore: DISABLE function calling completely for now

        # TODO: Enable when FPT Cloud supports automatic function calling
        # if tools and len(tools) > 0 and function_call_support_ui == "true" and model_supports_function_calling:
        #     extra_model_kwargs["tools"] = [
        #         self._convert_prompt_message_tool_to_dict(tool) for tool in tools
        #     ]

        # For now, do not send tools to avoid auto tool choice errors

        result = client.chat.completions.create(
            messages=[
                self._convert_prompt_message_to_dict(m) for m in prompt_messages
            ],
            model=model_name,
            stream=stream,
            **model_parameters,
            **extra_model_kwargs,
        )

        if stream:
            return self._handle_chat_generate_stream_response(
                model=model,
                credentials=credentials,
                response=result,
                tools=tools if model_supports_function_calling else None,
                prompt_messages=prompt_messages,
            )
        return self._handle_chat_generate_response(
            model=model,
            credentials=credentials,
            response=result,
            tools=tools if model_supports_function_calling else None,
            prompt_messages=prompt_messages,
        )

    def _to_client_kwargs(self, credentials: dict) -> dict:
        """
        Convert invoke kwargs to client kwargs
        """
        import os

        # Get server URL from credentials or fallback to environment variable
        server_url = credentials.get("server_url", os.getenv("BASE_URL", "https://mkp-api.fptcloud.com"))
        if not server_url.endswith("/"):
            server_url += "/"

        request_timeout = float(os.getenv("REQUEST_TIMEOUT", "120"))

        client_kwargs = {
            "timeout": Timeout(request_timeout + 15.0, read=request_timeout, write=10.0, connect=5.0),
            "api_key": credentials.get("api_key"),
            "base_url": server_url,
        }
        return client_kwargs

    def _model_supports_function_calling(self, model: str) -> bool:
        """
        Check if model supports function calling

        :param model: model name
        :return: True if model supports function calling
        """
        # BREAKTHROUGH: FPT Cloud API DOES support function calling!
        # - ❌ tool_choice="auto" requires server config (not available)
        # - ✅ tool_choice="none" works perfectly
        # - ✅ Forced function calls work
        # - ✅ Tools can be sent, just avoid auto tool choice

        # List of models that support function calling on FPT Cloud
        function_calling_models = [
            "QwQ-32B",
            "gemma-3-27b-it",
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4-turbo",
            "gpt-4o",
        ]

        # Check if model name contains any of the supported models
        model_lower = model.lower()
        for supported_model in function_calling_models:
            if supported_model.lower() in model_lower:
                return True

        # Default to False for unknown models
        return False

    def _convert_prompt_message_tool_to_dict(self, tool: PromptMessageTool) -> dict:
        """
        Convert PromptMessageTool to dict for OpenAI API

        :param tool: prompt message tool
        :return: tool dict
        """
        return {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.parameters,
            },
        }

    def _convert_prompt_message_to_dict(self, message: PromptMessage) -> dict:
        """
        Convert PromptMessage to dict for OpenAI Compatibility API
        """
        if isinstance(message, UserPromptMessage):
            message = cast(UserPromptMessage, message)
            if isinstance(message.content, str):
                message_dict = {"role": "user", "content": message.content}
            elif isinstance(message.content, list):
                # Handle vision content (text + images)
                content_list = []
                for content_item in message.content:
                    if content_item.type == "text":
                        content_list.append({
                            "type": "text",
                            "text": content_item.data
                        })
                    elif content_item.type == "image":
                        content_list.append({
                            "type": "image_url",
                            "image_url": {
                                "url": content_item.data
                            }
                        })
                message_dict = {"role": "user", "content": content_list}
            else:
                raise ValueError("User message content must be str or list")
        elif isinstance(message, AssistantPromptMessage):
            message = cast(AssistantPromptMessage, message)
            message_dict = {"role": "assistant", "content": message.content}
            if message.tool_calls and len(message.tool_calls) > 0:
                message_dict["tool_calls"] = [
                    {
                        "id": tool_call.id,
                        "type": "function",
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments,
                        }
                    }
                    for tool_call in message.tool_calls
                ]
        elif isinstance(message, SystemPromptMessage):
            message = cast(SystemPromptMessage, message)
            message_dict = {"role": "system", "content": message.content}
        else:
            raise ValueError(f"Unknown message type {type(message)}")
        return message_dict

    def _handle_chat_generate_response(
        self,
        model: str,
        prompt_messages: list[PromptMessage],
        credentials: dict,
        response: ChatCompletion,
        tools: list[PromptMessageTool],
    ) -> LLMResult:
        """
        Handle llm chat response
        """
        if len(response.choices) == 0:
            raise InvokeServerUnavailableError("Empty response")

        assistant_message = response.choices[0].message
        tool_calls = []
        if assistant_message.tool_calls:
            for tool_call in assistant_message.tool_calls:
                function = AssistantPromptMessage.ToolCall.ToolCallFunction(
                    name=tool_call.function.name,
                    arguments=tool_call.function.arguments
                )
                tool_calls.append(AssistantPromptMessage.ToolCall(
                    id=tool_call.id,
                    type="function",
                    function=function
                ))

        assistant_prompt_message = AssistantPromptMessage(
            content=assistant_message.content or "",
            tool_calls=tool_calls
        )

        prompt_tokens = self._num_tokens_from_messages(
            messages=prompt_messages, tools=tools or []
        )
        completion_tokens = self._num_tokens_from_messages(
            messages=[assistant_prompt_message], tools=[]
        )
        usage = self._calc_response_usage(
            model=model,
            credentials=credentials,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
        )

        response = LLMResult(
            model=model,
            prompt_messages=prompt_messages,
            system_fingerprint=response.system_fingerprint,
            usage=usage,
            message=assistant_prompt_message,
        )
        return response

    def _handle_chat_generate_stream_response(
        self,
        model: str,
        prompt_messages: list[PromptMessage],
        credentials: dict,
        response: Stream[ChatCompletionChunk],
        tools: list[PromptMessageTool],
    ) -> Generator:
        full_response = ""
        for chunk in response:
            if len(chunk.choices) == 0:
                continue
            delta = chunk.choices[0]
            if delta.finish_reason is None and (
                delta.delta.content is None or delta.delta.content == ""
            ):
                continue

            tool_calls = []
            if delta.delta.tool_calls:
                for tool_call in delta.delta.tool_calls:
                    if tool_call.function:
                        function = AssistantPromptMessage.ToolCall.ToolCallFunction(
                            name=tool_call.function.name or "",
                            arguments=tool_call.function.arguments or ""
                        )
                        tool_calls.append(AssistantPromptMessage.ToolCall(
                            id=tool_call.id or "",
                            type="function",
                            function=function
                        ))

            assistant_prompt_message = AssistantPromptMessage(
                content=delta.delta.content or "",
                tool_calls=tool_calls,
            )

            if delta.finish_reason is not None:
                temp_assistant_prompt_message = AssistantPromptMessage(
                    content=full_response, tool_calls=tool_calls
                )
                prompt_tokens = self._num_tokens_from_messages(
                    messages=prompt_messages, tools=tools or []
                )
                completion_tokens = self._num_tokens_from_messages(
                    messages=[temp_assistant_prompt_message], tools=[]
                )
                usage = self._calc_response_usage(
                    model=model,
                    credentials=credentials,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                )
                yield LLMResultChunk(
                    model=model,
                    prompt_messages=prompt_messages,
                    system_fingerprint=chunk.system_fingerprint,
                    delta=LLMResultChunkDelta(
                        index=delta.index,
                        message=assistant_prompt_message,
                        finish_reason=delta.finish_reason,
                        usage=usage,
                    ),
                )
            else:
                yield LLMResultChunk(
                    model=model,
                    prompt_messages=prompt_messages,
                    system_fingerprint=chunk.system_fingerprint,
                    delta=LLMResultChunkDelta(
                        index=delta.index, message=assistant_prompt_message
                    ),
                )
                full_response += delta.delta.content or ""

    @property
    def _invoke_error_mapping(self) -> dict[type[InvokeError], list[type[Exception]]]:
        """
        Map model invoke error to unified error
        """
        return {
            InvokeConnectionError: [APIConnectionError, APITimeoutError],
            InvokeServerUnavailableError: [
                InternalServerError,
                ConflictError,
                NotFoundError,
                UnprocessableEntityError,
                PermissionDeniedError,
            ],
            InvokeRateLimitError: [RateLimitError],
            InvokeAuthorizationError: [AuthenticationError],
            InvokeBadRequestError: [ValueError],
        }
