from typing import Optional
from dify_plugin.entities.model import (
    AIModelEntity,
    FetchFrom,
    I18nObject,
    ModelPropertyKey,
    ModelType,
)
from dify_plugin.entities.model.text_embedding import EmbeddingUsage, TextEmbeddingResult
from dify_plugin.errors.model import (
    CredentialsValidateFailedError,
    InvokeAuthorizationError,
    InvokeBadRequestError,
    InvokeConnectionError,
    InvokeError,
    InvokeRateLimitError,
    InvokeServerUnavailableError,
)
from dify_plugin.interfaces.model.text_embedding_model import TextEmbeddingModel
from httpx import Timeout
from openai import (
    APIConnectionError,
    APITimeoutError,
    AuthenticationError,
    ConflictError,
    InternalServerError,
    NotFoundError,
    OpenAI,
    PermissionDeniedError,
    RateLimitError,
    UnprocessableEntityError,
)


class FPTCloudTextEmbeddingModel(TextEmbeddingModel):
    def _invoke(
        self,
        model: str,
        credentials: dict,
        texts: list[str],
        user: str | None = None,
    ) -> TextEmbeddingResult:
        """
        Invoke text embedding model
        """
        kwargs = self._to_client_kwargs(credentials)
        client = OpenAI(**kwargs)
        
        extra_model_kwargs = {}
        if user:
            extra_model_kwargs["user"] = user
        
        try:
            response = client.embeddings.create(
                model=model,
                input=texts,
                **extra_model_kwargs
            )
            
            embeddings = []
            for data in response.data:
                embeddings.append(data.embedding)
            
            usage = EmbeddingUsage(
                tokens=response.usage.total_tokens if response.usage else 0,
                total_tokens=response.usage.total_tokens if response.usage else 0,
                unit_price=0,
                price_unit=0,
                total_price=0,
                currency="USD",
                latency=0
            )
            
            return TextEmbeddingResult(
                embeddings=embeddings,
                usage=usage,
                model=model
            )
            
        except Exception as ex:
            raise self._transform_invoke_error(ex)

    def get_num_tokens(self, model: str, credentials: dict, texts: list[str]) -> int:
        """
        Get number of tokens for given texts
        """
        # For simplicity, we'll estimate tokens based on character count
        total_chars = sum(len(text) for text in texts)
        # Rough estimation: 1 token ≈ 4 characters for most languages
        return total_chars // 4

    def validate_credentials(self, model: str, credentials: dict) -> None:
        """
        Validate model credentials
        """
        import os

        try:
            kwargs = self._to_client_kwargs(credentials)
            client = OpenAI(**kwargs)

            validation_timeout = float(os.getenv("VALIDATION_TIMEOUT", "30"))

            # Test with a simple embedding
            client.embeddings.create(
                model=model,
                input=["test"],
                timeout=validation_timeout
            )
        except Exception as ex:
            raise CredentialsValidateFailedError(f"Invalid credentials: {str(ex)}")

    def get_customizable_model_schema(
        self, model: str, credentials: dict
    ) -> Optional[AIModelEntity]:
        """
        Get customizable model schema
        """
        import os

        # Get configuration from credentials (user input) or environment variables
        context_size = int(credentials.get("context_size", os.getenv("CONTEXT_SIZE", "8192")))

        # Auto-detect dimensions based on model name (this is harder for user to know)
        if "Vietnamese" in model or "vietnamese" in model:
            dimensions = 1024
        elif "e5" in model.lower():
            dimensions = 1024
        elif "gte" in model.lower():
            dimensions = 768
        else:
            # Default fallback
            dimensions = 1024

        return AIModelEntity(
            model=model,
            label=I18nObject(en_US=model),
            fetch_from=FetchFrom.CUSTOMIZABLE_MODEL,
            model_type=ModelType.TEXT_EMBEDDING,
            model_properties={
                ModelPropertyKey.CONTEXT_SIZE: context_size,
                ModelPropertyKey.MAX_CHUNKS: 1,
                ModelPropertyKey.TEXT_EMBEDDING_DIMENSIONS: dimensions,
            }
        )

    def _to_client_kwargs(self, credentials: dict) -> dict:
        """
        Convert invoke kwargs to client kwargs
        """
        import os

        # Get server URL from credentials or fallback to environment variable
        server_url = credentials.get("server_url", os.getenv("BASE_URL", "https://mkp-api.fptcloud.com"))
        if not server_url.endswith("/"):
            server_url += "/"

        request_timeout = float(os.getenv("REQUEST_TIMEOUT", "120"))

        client_kwargs = {
            "timeout": Timeout(request_timeout + 15.0, read=request_timeout, write=10.0, connect=5.0),
            "api_key": credentials.get("api_key"),
            "base_url": server_url,
        }
        return client_kwargs

    def _transform_invoke_error(self, error: Exception) -> InvokeError:
        """
        Transform invoke error to unified error
        """
        if isinstance(error, InvokeError):
            return error
        else:
            error_str = str(error)
            if "unauthorized" in error_str.lower() or "authentication" in error_str.lower():
                return InvokeAuthorizationError(error_str)
            elif "rate limit" in error_str.lower():
                return InvokeRateLimitError(error_str)
            elif "bad request" in error_str.lower():
                return InvokeBadRequestError(error_str)
            elif "server" in error_str.lower():
                return InvokeServerUnavailableError(error_str)
            elif "connection" in error_str.lower():
                return InvokeConnectionError(error_str)
            else:
                return InvokeError(error_str)

    @property
    def _invoke_error_mapping(self) -> dict[type[InvokeError], list[type[Exception]]]:
        """
        Map model invoke error to unified error
        """
        return {
            InvokeConnectionError: [APIConnectionError, APITimeoutError],
            InvokeServerUnavailableError: [
                InternalServerError,
                ConflictError,
                NotFoundError,
                UnprocessableEntityError,
                PermissionDeniedError,
            ],
            InvokeRateLimitError: [RateLimitError],
            InvokeAuthorizationError: [AuthenticationError],
            InvokeBadRequestError: [ValueError],
        }
