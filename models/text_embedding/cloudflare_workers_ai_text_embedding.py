import logging
from typing import Optional

import httpx
from dify_plugin.entities.model import (
    AIModelEntity,
    FetchFrom,
    I18nObject,
    ModelPropertyKey,
    ModelType,
    PriceConfig,
)
from dify_plugin.entities.model.text_embedding import EmbeddingUsage, TextEmbeddingResult
from dify_plugin.errors.model import (
    CredentialsValidateFailedError,
    InvokeAuthorizationError,
    InvokeBadRequestError,
    InvokeConnectionError,
    InvokeError,
    InvokeRateLimitError,
    InvokeServerUnavailableError,
)
from dify_plugin.interfaces.model.text_embedding_model import TextEmbeddingModel

logger = logging.getLogger(__name__)


class CloudflareWorkersAITextEmbeddingModel(TextEmbeddingModel):
    def _invoke(
        self,
        model: str,
        credentials: dict,
        texts: list[str],
        user: str | None = None,
    ) -> TextEmbeddingResult:
        """
        Invoke text embedding model
        """
        # Get credentials from model configuration
        api_token = credentials.get("api_token")
        account_id = credentials.get("account_id")

        # Fallback to provider credentials if not set in model
        if not account_id:
            account_id = credentials.get("default_account_id")

        if not api_token:
            raise CredentialsValidateFailedError("API token is required for this model")
        if not account_id:
            raise CredentialsValidateFailedError("Account ID is required (set in model or provider configuration)")

        # Build API URL
        base_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/ai/run/{model}"
        
        headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json",
        }
        
        try:
            embeddings = []
            total_tokens = 0
            
            for text in texts:
                payload = {"text": text}
                
                with httpx.Client() as client:
                    response = client.post(base_url, headers=headers, json=payload, timeout=120.0)
                    response.raise_for_status()
                    
                    result = response.json()
                    
                    # Extract embedding from response
                    if "result" in result and "data" in result["result"]:
                        embedding = result["result"]["data"]
                        embeddings.append(embedding)
                        # Estimate tokens (rough approximation)
                        total_tokens += len(text.split())
                    else:
                        raise InvokeError("Invalid response format from Cloudflare Workers AI")
            
            usage = EmbeddingUsage(
                tokens=total_tokens,
                total_tokens=total_tokens,
                unit_price=0,
                price_unit=0,
                total_price=0,
                currency="USD",
                latency=0
            )
            
            return TextEmbeddingResult(
                embeddings=embeddings,
                usage=usage,
                model=model
            )
            
        except Exception as ex:
            raise self._transform_invoke_error(ex)

    def get_num_tokens(self, model: str, credentials: dict, texts: list[str]) -> int:
        """
        Get number of tokens for given texts
        """
        # Simple approximation - count words
        return sum(len(text.split()) for text in texts)

    def validate_credentials(self, model: str, credentials: dict) -> None:
        """
        Validate model credentials
        """
        try:
            # Test with a simple embedding
            self._invoke(
                model=model,
                credentials=credentials,
                texts=["test"]
            )
        except Exception as ex:
            raise CredentialsValidateFailedError(f"Invalid credentials: {str(ex)}")

    def get_customizable_model_schema(
        self, model: str, credentials: dict
    ) -> Optional[AIModelEntity]:
        """
        Get customizable model schema
        """
        import os

        # Get configuration from credentials (user input) or environment variables
        context_size = int(credentials.get("context_size", os.getenv("CONTEXT_SIZE", "8192")))

        # Auto-detect dimensions based on model name
        if "bge" in model.lower():
            dimensions = 1024
        elif "e5" in model.lower():
            dimensions = 1024
        elif "gte" in model.lower():
            dimensions = 768
        else:
            # Default fallback for Cloudflare Workers AI
            dimensions = 768

        entity = AIModelEntity(
            model=model,
            label=I18nObject(en_US=model),
            model_type=ModelType.TEXT_EMBEDDING,
            fetch_from=FetchFrom.CUSTOMIZABLE_MODEL,
            model_properties={
                ModelPropertyKey.CONTEXT_SIZE: context_size,
                ModelPropertyKey.MAX_CHUNKS: 1,
            },
            parameter_rules=[],
            pricing=PriceConfig(
                input=0,
                unit=0,
                currency="USD",
            ),
        )
        return entity

    def _transform_invoke_error(self, error: Exception) -> InvokeError:
        """Transform invoke error to Dify error"""
        if isinstance(error, httpx.HTTPStatusError):
            if error.response.status_code == 401:
                return InvokeAuthorizationError("Invalid API token")
            elif error.response.status_code == 400:
                return InvokeBadRequestError(str(error))
            elif error.response.status_code == 429:
                return InvokeRateLimitError("Rate limit exceeded")
            elif error.response.status_code >= 500:
                return InvokeServerUnavailableError("Server unavailable")
            else:
                return InvokeError(str(error))
        elif isinstance(error, httpx.ConnectError):
            return InvokeConnectionError("Connection failed")
        elif isinstance(error, httpx.TimeoutException):
            return InvokeConnectionError("Request timeout")
        else:
            return InvokeError(str(error))
