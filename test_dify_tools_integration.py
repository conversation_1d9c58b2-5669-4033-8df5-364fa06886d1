#!/usr/bin/env python3
"""
Test để kiểm tra tools integration với Dify
Simulate cách Dify gọi tools
"""
import json
from openai import OpenAI

# Configuration
API_KEY = "sk-HMHa6NNBNnJdWfl_USuxuQ"
BASE_URL = "https://mkp-api.fptcloud.com"

def test_tool_choice_scenarios():
    """Test các scenarios tool_choice khác nhau"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get current weather information for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city name"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    scenarios = [
        {
            "name": "No tool_choice (default auto)",
            "tool_choice": None,
            "description": "Let API decide automatically"
        },
        {
            "name": "tool_choice='none'",
            "tool_choice": "none",
            "description": "Never call functions (current plugin setting)"
        },
        {
            "name": "tool_choice='auto'",
            "tool_choice": "auto", 
            "description": "Let API decide (explicit)"
        },
        {
            "name": "Forced function call",
            "tool_choice": {"type": "function", "function": {"name": "get_weather"}},
            "description": "Force call specific function"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{'='*60}")
        print(f"🧪 Testing: {scenario['name']}")
        print(f"📝 Description: {scenario['description']}")
        print(f"{'='*60}")
        
        try:
            # Prepare request
            request_params = {
                "model": "QwQ-32B",
                "messages": [
                    {
                        "role": "user",
                        "content": "What's the weather like in Hanoi today? Please use the weather function if available."
                    }
                ],
                "tools": tools,
                "temperature": 0.1
            }
            
            # Add tool_choice if specified
            if scenario["tool_choice"] is not None:
                request_params["tool_choice"] = scenario["tool_choice"]
            
            response = client.chat.completions.create(**request_params)
            
            print(f"✅ SUCCESS!")
            print(f"📝 Response: {response.choices[0].message.content[:100]}...")
            
            if response.choices[0].message.tool_calls:
                print(f"🔧 Tool calls made:")
                for tool_call in response.choices[0].message.tool_calls:
                    print(f"   - Function: {tool_call.function.name}")
                    print(f"   - Arguments: {tool_call.function.arguments}")
            else:
                print(f"❌ No tool calls made")
                
        except Exception as e:
            print(f"❌ FAILED: {str(e)}")

def test_dify_workflow_simulation():
    """Simulate cách Dify sẽ gọi function calling"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    print(f"\n{'='*60}")
    print(f"🎯 Simulating Dify Workflow")
    print(f"{'='*60}")
    
    # Step 1: Dify sends tools và expects model to decide
    tools = [
        {
            "type": "function",
            "function": {
                "name": "search_web",
                "description": "Search the web for information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"}
                    },
                    "required": ["query"]
                }
            }
        }
    ]
    
    print(f"\n📋 Step 1: User asks question that needs web search")
    try:
        # Dify typically doesn't set tool_choice, expects model to decide
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {
                    "role": "system",
                    "content": "You are a helpful assistant. Use the available tools when needed to provide accurate information."
                },
                {
                    "role": "user",
                    "content": "What are the latest news about AI developments? Please search for recent information."
                }
            ],
            tools=tools,
            # No tool_choice - let model decide
            temperature=0.1
        )
        
        print(f"✅ Model response received")
        print(f"📝 Content: {response.choices[0].message.content[:100] if response.choices[0].message.content else 'None'}...")
        
        if response.choices[0].message.tool_calls:
            print(f"🔧 Model decided to call tools:")
            for tool_call in response.choices[0].message.tool_calls:
                print(f"   - Function: {tool_call.function.name}")
                print(f"   - Arguments: {tool_call.function.arguments}")
                
            # Step 2: Dify executes the tool and sends result back
            print(f"\n📋 Step 2: Dify executes tool and sends result back")
            
            # Simulate tool execution result
            tool_result = {
                "role": "tool",
                "tool_call_id": response.choices[0].message.tool_calls[0].id,
                "content": "Latest AI news: GPT-5 announced, new breakthrough in quantum computing AI, etc."
            }
            
            # Continue conversation with tool result
            final_response = client.chat.completions.create(
                model="QwQ-32B",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant. Use the available tools when needed."
                    },
                    {
                        "role": "user", 
                        "content": "What are the latest news about AI developments? Please search for recent information."
                    },
                    {
                        "role": "assistant",
                        "content": response.choices[0].message.content,
                        "tool_calls": [
                            {
                                "id": tool_call.id,
                                "type": "function",
                                "function": {
                                    "name": tool_call.function.name,
                                    "arguments": tool_call.function.arguments
                                }
                            } for tool_call in response.choices[0].message.tool_calls
                        ]
                    },
                    tool_result
                ],
                tools=tools,
                temperature=0.1
            )
            
            print(f"✅ Final response with tool results:")
            print(f"📝 {final_response.choices[0].message.content[:200]}...")
            
        else:
            print(f"❌ Model did NOT call any tools - this is the problem!")
            print(f"🔍 This means tool_choice='none' is preventing function calls")
            
    except Exception as e:
        print(f"❌ Dify workflow simulation failed: {str(e)}")

if __name__ == "__main__":
    print("🔍 Testing Tools Integration with Dify")
    print("=" * 60)
    
    # Test different tool_choice scenarios
    test_tool_choice_scenarios()
    
    # Test Dify workflow simulation
    test_dify_workflow_simulation()
    
    print(f"\n{'='*60}")
    print("🎯 ANALYSIS:")
    print("- tool_choice='none' = Model NEVER calls functions")
    print("- No tool_choice = Model decides when to call (DIFY NEEDS THIS)")
    print("- tool_choice='auto' = Same as no tool_choice but explicit")
    print("- Forced calls = Specific function must be called")
    print("=" * 60)
