#!/usr/bin/env python3
"""
Debug script để test plugin AIDiBiz FPT Cloud với Dify
"""

import os
import sys
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Dify configuration
DIFY_API_URL = os.getenv("DIFY_API_URL", "http://localhost/v1")
DIFY_API_KEY = os.getenv("DIFY_API_KEY", "")

# FPT Cloud configuration
FPT_API_KEY = os.getenv("API_KEY", "")
FPT_BASE_URL = os.getenv("BASE_URL", "https://mkp-api.fptcloud.com")

def test_dify_connection():
    """Test connection to Dify API"""
    print("🔍 Testing Dify connection...")
    
    if not DIFY_API_KEY:
        print("❌ DIFY_API_KEY not found in environment variables")
        return False
    
    try:
        headers = {
            "Authorization": f"Bearer {DIFY_API_KEY}",
            "Content-Type": "application/json"
        }
        
        # Test với endpoint health check hoặc apps
        response = requests.get(f"{DIFY_API_URL}/apps", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ Dify connection successful")
            return True
        else:
            print(f"❌ Dify connection failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Dify connection error: {str(e)}")
        return False

def test_fpt_cloud_connection():
    """Test connection to FPT Cloud API"""
    print("🔍 Testing FPT Cloud connection...")
    
    if not FPT_API_KEY:
        print("❌ FPT_API_KEY not found in environment variables")
        return False
    
    try:
        from openai import OpenAI
        
        client = OpenAI(
            api_key=FPT_API_KEY,
            base_url=FPT_BASE_URL
        )
        
        # Test với một request đơn giản
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {"role": "user", "content": "Hello"}
            ],
            max_tokens=10
        )
        
        print("✅ FPT Cloud connection successful")
        print(f"Response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ FPT Cloud connection error: {str(e)}")
        return False

def check_plugin_files():
    """Check if plugin files exist and are valid"""
    print("🔍 Checking plugin files...")
    
    required_files = [
        "manifest.yaml",
        "main.py",
        "provider/fpt-cloud.yaml",
        "provider/fpt_cloud.py",
        "models/llm/llm.py",
        "models/text_embedding/text_embedding.py",
        "models/multimodal_llm/multimodal_llm.py",
        "aidibiz-fpt-cloud.signed.difypkg"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing plugin files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All plugin files exist")
        return True

def validate_plugin_structure():
    """Validate plugin structure"""
    print("🔍 Validating plugin structure...")
    
    try:
        # Check manifest.yaml
        import yaml
        with open("manifest.yaml", "r") as f:
            manifest = yaml.safe_load(f)
        
        required_fields = ["name", "version", "type", "plugins"]
        for field in required_fields:
            if field not in manifest:
                print(f"❌ Missing field in manifest.yaml: {field}")
                return False
        
        print("✅ Plugin structure is valid")
        return True
        
    except Exception as e:
        print(f"❌ Plugin structure validation error: {str(e)}")
        return False

def test_plugin_import():
    """Test if plugin modules can be imported"""
    print("🔍 Testing plugin imports...")
    
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.getcwd())
        
        # Test provider import
        from provider.fpt_cloud import FPTCloudProvider
        print("✅ Provider import successful")
        
        # Test LLM import
        from models.llm.llm import FPTCloudLanguageModel
        print("✅ LLM import successful")
        
        # Test embedding import
        from models.text_embedding.text_embedding import FPTCloudTextEmbeddingModel
        print("✅ Text embedding import successful")
        
        # Test multimodal import
        from models.multimodal_llm.multimodal_llm import FPTCloudMultimodalLargeLanguageModel
        print("✅ Multimodal LLM import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Plugin import error: {str(e)}")
        return False

def simulate_plugin_validation():
    """Simulate plugin validation like Dify would do"""
    print("🔍 Simulating plugin validation...")
    
    try:
        # Test provider validation
        from provider.fpt_cloud import FPTCloudProvider
        
        provider = FPTCloudProvider()
        credentials = {
            "api_key": FPT_API_KEY
        }
        
        provider.validate_provider_credentials(credentials)
        print("✅ Provider validation successful")
        
        # Test LLM validation
        from models.llm.llm import FPTCloudLanguageModel
        
        llm = FPTCloudLanguageModel()
        llm.validate_credentials("QwQ-32B", credentials)
        print("✅ LLM validation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Plugin validation error: {str(e)}")
        return False

def main():
    """Main debug function"""
    print("🚀 Starting AIDiBiz FPT Cloud Plugin Debug")
    print("=" * 50)
    
    # Run all tests
    tests = [
        ("Plugin Files", check_plugin_files),
        ("Plugin Structure", validate_plugin_structure),
        ("Plugin Imports", test_plugin_import),
        ("FPT Cloud Connection", test_fpt_cloud_connection),
        ("Plugin Validation", simulate_plugin_validation),
        ("Dify Connection", test_dify_connection),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DEBUG SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Plugin is ready for deployment.")
    else:
        print("⚠️  Some tests failed. Please fix the issues before deployment.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
