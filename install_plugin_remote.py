#!/usr/bin/env python3
"""
Script để cài đặt plugin vào Dify remote server
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Remote installation configuration
INSTALL_METHOD = os.getenv("INSTALL_METHOD", "local")
REMOTE_HOST = os.getenv("REMOTE_INSTALL_HOST", "localhost")
REMOTE_PORT = os.getenv("REMOTE_INSTALL_PORT", "5003")
REMOTE_KEY = os.getenv("REMOTE_INSTALL_KEY", "")

PLUGIN_FILE = "aidibiz_fpt_cloud.signed.difypkg"

def install_plugin_remote():
    """Install plugin to remote Dify server"""
    print(f"🚀 Installing plugin to remote Dify server")
    print(f"Host: {REMOTE_HOST}:{REMOTE_PORT}")
    print(f"Plugin: {PLUGIN_FILE}")
    print("=" * 50)
    
    if not os.path.exists(PLUGIN_FILE):
        print(f"❌ Plugin file not found: {PLUGIN_FILE}")
        return False
    
    if not REMOTE_KEY:
        print("❌ REMOTE_INSTALL_KEY not found in .env")
        return False
    
    # Prepare the request
    url = f"http://{REMOTE_HOST}:{REMOTE_PORT}/v1/plugins/install"
    
    headers = {
        "Authorization": f"Bearer {REMOTE_KEY}",
    }
    
    try:
        # Read plugin file
        with open(PLUGIN_FILE, 'rb') as f:
            plugin_data = f.read()
        
        files = {
            'plugin': (PLUGIN_FILE, plugin_data, 'application/octet-stream')
        }
        
        print("📤 Uploading plugin...")
        response = requests.post(url, headers=headers, files=files, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Plugin installed successfully!")
            print(f"Plugin ID: {result.get('plugin_id', 'N/A')}")
            print(f"Status: {result.get('status', 'N/A')}")
            return True
        else:
            print(f"❌ Installation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to {REMOTE_HOST}:{REMOTE_PORT}")
        print("Please check if Dify server is running and accessible")
        return False
    except Exception as e:
        print(f"❌ Installation error: {str(e)}")
        return False

def check_plugin_status():
    """Check plugin status on remote server"""
    print("🔍 Checking plugin status...")
    
    url = f"http://{REMOTE_HOST}:{REMOTE_PORT}/v1/plugins"
    headers = {
        "Authorization": f"Bearer {REMOTE_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            plugins = response.json()
            
            # Look for our plugin
            for plugin in plugins.get('data', []):
                if 'aidibiz' in plugin.get('name', '').lower():
                    print(f"✅ Plugin found: {plugin.get('name')}")
                    print(f"   Version: {plugin.get('version', 'N/A')}")
                    print(f"   Status: {plugin.get('status', 'N/A')}")
                    print(f"   Author: {plugin.get('author', 'N/A')}")
                    return True
            
            print("❌ Plugin not found in installed plugins")
            print("Available plugins:")
            for plugin in plugins.get('data', []):
                print(f"   - {plugin.get('name', 'Unknown')}")
            return False
        else:
            print(f"❌ Cannot check plugin status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Status check error: {str(e)}")
        return False

def configure_provider():
    """Configure FPT Cloud provider on remote server"""
    print("🔧 Configuring FPT Cloud provider...")
    
    api_key = os.getenv("API_KEY", "")
    if not api_key:
        print("❌ API_KEY not found in .env")
        return False
    
    url = f"http://{REMOTE_HOST}:{REMOTE_PORT}/v1/workspaces/current/model-providers/fpt-cloud/credentials"
    headers = {
        "Authorization": f"Bearer {REMOTE_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "credentials": {
            "api_key": api_key
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code in [200, 201]:
            print("✅ FPT Cloud provider configured successfully")
            return True
        else:
            print(f"❌ Provider configuration failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Provider configuration error: {str(e)}")
        return False

def test_models():
    """Test the models on remote server"""
    print("🧪 Testing models...")
    
    # Test LLM
    print("Testing LLM (QwQ-32B)...")
    llm_url = f"http://{REMOTE_HOST}:{REMOTE_PORT}/v1/workspaces/current/model-providers/fpt-cloud/models/llm/QwQ-32B/test"
    headers = {
        "Authorization": f"Bearer {REMOTE_KEY}",
        "Content-Type": "application/json"
    }
    
    llm_data = {
        "model": "QwQ-32B",
        "messages": [
            {
                "role": "user",
                "content": "Hello, respond with just 'OK'"
            }
        ],
        "max_tokens": 10
    }
    
    try:
        response = requests.post(llm_url, headers=headers, json=llm_data, timeout=30)
        if response.status_code == 200:
            print("✅ LLM test successful")
        else:
            print(f"❌ LLM test failed: {response.status_code}")
    except Exception as e:
        print(f"❌ LLM test error: {str(e)}")
    
    # Test Embedding
    print("Testing Embedding (Vietnamese_Embedding)...")
    emb_url = f"http://{REMOTE_HOST}:{REMOTE_PORT}/v1/workspaces/current/model-providers/fpt-cloud/models/text-embedding/Vietnamese_Embedding/test"
    
    emb_data = {
        "model": "Vietnamese_Embedding",
        "input": "Test embedding"
    }
    
    try:
        response = requests.post(emb_url, headers=headers, json=emb_data, timeout=30)
        if response.status_code == 200:
            print("✅ Embedding test successful")
        else:
            print(f"❌ Embedding test failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Embedding test error: {str(e)}")

def main():
    """Main function"""
    print("🚀 AIDiBiz FPT Cloud Plugin - Remote Installation")
    print("=" * 50)
    
    if INSTALL_METHOD != "remote":
        print(f"❌ INSTALL_METHOD is '{INSTALL_METHOD}', expected 'remote'")
        print("Please update .env file")
        return False
    
    # Step 1: Install plugin
    if not install_plugin_remote():
        print("❌ Plugin installation failed")
        return False
    
    # Step 2: Check plugin status
    if not check_plugin_status():
        print("❌ Plugin status check failed")
        return False
    
    # Step 3: Configure provider
    if not configure_provider():
        print("❌ Provider configuration failed")
        return False
    
    # Step 4: Test models
    test_models()
    
    print("\n🎉 Remote installation completed!")
    print(f"Plugin URL: http://{REMOTE_HOST}:{REMOTE_PORT}")
    print("You can now use FPT Cloud AI models in your Dify applications.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
