#!/usr/bin/env python3
"""
Test trực tiếp với OpenAI client như trong tài liệu FPT Cloud
Không dùng LiteLLM, test function calling thuần
"""
import json
from openai import OpenAI

# Configuration từ tài liệu FPT Cloud
API_KEY = "sk-HMHa6NNBNnJdWfl_USuxuQ"
BASE_URL = "https://mkp-api.fptcloud.com"

def test_function_calling_direct():
    """Test function calling trực tiếp với OpenAI client"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    # Define function theo chuẩn OpenAI
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get current weather information for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city name, e.g. <PERSON>, Ho Chi Minh City"
                        },
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "Temperature unit"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    models_to_test = ["QwQ-32B", "gemma-3-27b-it"]
    
    for model in models_to_test:
        print(f"\n{'='*60}")
        print(f"🧪 Testing Function Calling with {model}")
        print(f"{'='*60}")
        
        # Test 1: Function calling với auto tool choice (default)
        print(f"\n📋 Test 1: Function calling (auto tool choice)")
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant. Use the available functions when appropriate."
                    },
                    {
                        "role": "user",
                        "content": "What's the weather like in Hanoi today?"
                    }
                ],
                tools=tools,
                temperature=0.1
            )
            
            print(f"✅ SUCCESS: Function calling works with {model}!")
            print(f"Response: {response.choices[0].message.content}")
            
            if response.choices[0].message.tool_calls:
                print(f"🔧 Tool calls detected:")
                for tool_call in response.choices[0].message.tool_calls:
                    print(f"  - Function: {tool_call.function.name}")
                    print(f"  - Arguments: {tool_call.function.arguments}")
            else:
                print("ℹ️  No tool calls made (model chose to respond directly)")
                
        except Exception as e:
            print(f"❌ FAILED: {str(e)}")
            
            # Test 2: Với tool_choice="auto" explicit
            print(f"\n📋 Test 2: Explicit tool_choice='auto'")
            try:
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {
                            "role": "user",
                            "content": "What's the weather like in Hanoi?"
                        }
                    ],
                    tools=tools,
                    tool_choice="auto",
                    temperature=0.1
                )
                print(f"✅ SUCCESS with explicit auto: {model}")
                print(f"Response: {response.choices[0].message.content}")
                
            except Exception as e2:
                print(f"❌ FAILED with explicit auto: {str(e2)}")
                
                # Test 3: Với tool_choice="none"
                print(f"\n📋 Test 3: Force tool_choice='none'")
                try:
                    response = client.chat.completions.create(
                        model=model,
                        messages=[
                            {
                                "role": "user",
                                "content": "What's the weather like in Hanoi?"
                            }
                        ],
                        tools=tools,
                        tool_choice="none",
                        temperature=0.1
                    )
                    print(f"✅ SUCCESS with tool_choice='none': {model}")
                    print(f"Response: {response.choices[0].message.content[:200]}...")
                    
                except Exception as e3:
                    print(f"❌ FAILED even with tool_choice='none': {str(e3)}")

def test_function_calling_specific_function():
    """Test với tool_choice chỉ định function cụ thể"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get weather information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {"type": "string"}
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    print(f"\n{'='*60}")
    print(f"🎯 Testing Forced Function Call")
    print(f"{'='*60}")
    
    try:
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {
                    "role": "user",
                    "content": "What's the weather in Hanoi?"
                }
            ],
            tools=tools,
            tool_choice={"type": "function", "function": {"name": "get_weather"}},
            temperature=0.1
        )
        
        print(f"✅ SUCCESS: Forced function call works!")
        if response.choices[0].message.tool_calls:
            for tool_call in response.choices[0].message.tool_calls:
                print(f"🔧 Called: {tool_call.function.name}")
                print(f"📝 Args: {tool_call.function.arguments}")
        
    except Exception as e:
        print(f"❌ FAILED forced function call: {str(e)}")

def test_without_tools():
    """Test baseline - không có tools"""
    
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    print(f"\n{'='*60}")
    print(f"📊 Baseline Test - No Tools")
    print(f"{'='*60}")
    
    try:
        response = client.chat.completions.create(
            model="QwQ-32B",
            messages=[
                {
                    "role": "user",
                    "content": "What's the weather like in Hanoi?"
                }
            ],
            temperature=0.1
        )
        
        print(f"✅ SUCCESS: Baseline works perfectly")
        print(f"Response: {response.choices[0].message.content[:200]}...")
        
    except Exception as e:
        print(f"❌ FAILED baseline: {str(e)}")

if __name__ == "__main__":
    print("🚀 FPT Cloud API Direct OpenAI Client Test")
    print("=" * 60)
    print("Testing function calling với OpenAI client như trong tài liệu")
    print("KHÔNG sử dụng LiteLLM")
    
    # Test function calling
    test_function_calling_direct()
    
    # Test forced function call
    test_function_calling_specific_function()
    
    # Test baseline
    test_without_tools()
    
    print(f"\n{'='*60}")
    print("🎯 CONCLUSION:")
    print("- Nếu function calling hoạt động → FPT Cloud hỗ trợ function calling")
    print("- Nếu tất cả fail → Vấn đề ở server configuration")
    print("- Nếu chỉ baseline hoạt động → Function calling chưa được enable")
    print("=" * 60)
