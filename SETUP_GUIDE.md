# AIDiBiz FPT Cloud Plugin - Setup Guide

## 🎯 Plugin Features

✅ **Customizable Model Support** - Giống như LocalAI/Ollama plugin  
✅ **Dynamic Context Size** - Tự động detect dựa trên tên model  
✅ **Flexible Configuration** - Không cần hardcode model names  
✅ **Environment Variables** - Configurable parameters  

## 🚀 Setup trong Dify

### 1. Upload Plugin
1. Đăng nhập Dify admin panel
2. <PERSON><PERSON> đến **Settings** > **Model Providers**
3. Click **Upload Plugin**
4. Upload file: `aidibiz_fpt_cloud.signed.difypkg`

### 2. Setup FPT Cloud AI Provider
Sau khi upload thành công, bạn sẽ thấy giao diện setup:

```
Setup FPT Cloud AI
┌─────────────────────────────────────┐
│ Model Type:         [LLM ▼]         │
│ Model Name:         [QwQ-32B    ]   │
│ API Key:            [••••••••••••]  │
│ Context Size:       [32768      ]   │
│ Max Tokens:         [4096       ]   │
│ Model Features:     [Basic ▼    ]   │
│                                     │
│ [How to get your FPT Cloud AI API]  │
└─────────────────────────────────────┘
```

### 3. Configuration Fields

#### Model Type
- **LLM**: Cho chat/completion models
- **Text Embedding**: Cho embedding models

#### Model Name (Customizable)
Bạn có thể nhập bất kỳ model name nào từ FPT Cloud:

**LLM Models:**
- `QwQ-32B`
- `Qwen2.5-7B-Instruct`
- `Qwen2.5-70B-Instruct`
- Hoặc bất kỳ model nào khác

**Embedding Models:**
- `Vietnamese_Embedding`
- `FPT.AI-e5-large`
- `FPT.AI-gte-base`

#### API Key
- Nhập API key từ FPT Cloud AI Marketplace
- Format: `sk-xxxxxxxxxxxxxxxxxx`

#### Context Size (Customizable)
- Nhập kích thước ngữ cảnh của model
- Ví dụ: `4096`, `8192`, `32768`, `65536`
- Default: `8192` nếu không nhập

#### Max Tokens (Customizable)
- Nhập số token tối đa cho response
- Ví dụ: `1024`, `2048`, `4096`, `8192`
- Default: `4096` nếu không nhập

#### Model Features (Dropdown)
- Dropdown để chọn tính năng model hỗ trợ
- **Basic (Text only)**: Chỉ xử lý text thông thường
- **Tool Calling**: Hỗ trợ function calling/tools
- **Vision (Image processing)**: Hỗ trợ xử lý hình ảnh
- **Tool Calling + Vision**: Hỗ trợ cả function calling và vision
- Default: `Basic` (chỉ text)

## 🔧 Configuration Features

### User-Configurable Parameters
Plugin cho phép user tự cấu hình:

- **Context Size**: User nhập trực tiếp trong UI
- **Max Tokens**: User nhập trực tiếp trong UI
- **Model Name**: User nhập tên model bất kỳ

### Auto-Detection (Chỉ cho Embedding Dimensions)
```python
# Embedding Models - dimensions được auto-detect
if "Vietnamese" in model_name:
    dimensions = 1024
elif "e5" in model_name:
    dimensions = 1024
elif "gte" in model_name:
    dimensions = 768
else:
    dimensions = 1024     # Default
```

### Fallback Values
Nếu user không nhập, sẽ sử dụng default values:
- Context Size: `8192`
- Max Tokens: `4096`
- Embedding Dimensions: `1024`

## 📋 Setup Examples

### Example 1: QwQ-32B LLM (Basic)
```
Model Type: LLM
Model Name: QwQ-32B
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: 32768
Max Tokens: 4096
Model Features: Basic (Text only)
```

### Example 2: Vision Model
```
Model Type: LLM
Model Name: gemma-3-27b-it
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: 8192
Max Tokens: 4096
Model Features: Vision (Image processing)
```

### Example 3: Function Calling Model
```
Model Type: LLM
Model Name: qwen-plus-function
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: 16384
Max Tokens: 2048
Model Features: Tool Calling
```

### Example 4: Full Featured Model
```
Model Type: LLM
Model Name: advanced-multimodal-model
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: 16384
Max Tokens: 4096
Model Features: Tool Calling + Vision
```

### Example 5: Vietnamese Embedding
```
Model Type: Text Embedding
Model Name: Vietnamese_Embedding
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: 8192
Max Tokens: (không cần cho embedding)
Model Features: (không áp dụng cho embedding)
```

### Example 6: Sử dụng Default Values
```
Model Type: LLM
Model Name: any-model-name
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: (để trống - sẽ dùng 8192)
Max Tokens: (để trống - sẽ dùng 4096)
Model Features: Basic (default)
```

## ⚙️ Advanced Configuration

### Environment Variables
Plugin sử dụng các biến môi trường để cấu hình default values:

```env
# Model Parameters
MAX_TOKENS=4096
TEMPERATURE=0.7
TOP_P=0.9
CONTEXT_SIZE=8192

# Timeout Settings
REQUEST_TIMEOUT=120
VALIDATION_TIMEOUT=30
```

### Model Parameters trong Dify
Sau khi setup model, bạn có thể điều chỉnh:

- **Temperature** (0.0-2.0): Creativity level
- **Top P** (0.0-1.0): Nucleus sampling
- **Max Tokens**: Maximum response length

## 🔍 Troubleshooting

### 1. Model Validation Failed
```
Error: 504 Gateway Timeout
```
**Solution:**
- Kiểm tra API key đúng chưa
- Thử model name khác
- Tăng timeout trong environment variables

### 2. Context Size Issues
```
Error: Context length exceeded
```
**Solution:**
- Plugin tự động detect context size
- Nếu sai, có thể override bằng environment variable
- Kiểm tra tên model có đúng format không

### 3. Model Not Found
```
Error: Model not found
```
**Solution:**
- Kiểm tra model name chính xác
- Verify model có available trong FPT Cloud không
- Thử với model khác như QwQ-32B

## 🎉 Success Indicators

Khi setup thành công, bạn sẽ thấy:

1. ✅ **Model Provider**: FPT Cloud AI hiển thị trong danh sách
2. ✅ **Model Available**: Model xuất hiện trong dropdown khi tạo app
3. ✅ **Test Success**: Có thể test model trong Dify interface

## 📝 Best Practices

1. **Bắt đầu với QwQ-32B** - Model stable và được test kỹ
2. **Sử dụng tên model chuẩn** - Để auto-detection hoạt động tốt
3. **Test trước khi deploy** - Verify model hoạt động trong Dify
4. **Monitor performance** - Theo dõi response time và quality

## 🚀 Ready to Use!

Sau khi setup thành công:
1. Tạo new App trong Dify
2. Chọn Model Provider: **FPT Cloud AI**
3. Chọn model đã setup
4. Bắt đầu sử dụng!

Plugin sẽ tự động sử dụng context size và parameters phù hợp cho từng model.
