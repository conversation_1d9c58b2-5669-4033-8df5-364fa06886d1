# AIDiBiz FPT Cloud Plugin - Configuration Guide

## 📋 Environment Variables Configuration

Plugin hiện hỗ trợ cấu hình linh hoạt thông qua các biến môi trường trong file `.env`:

### 🔑 API Configuration
```env
API_KEY="sk-HMHa6NNBNnJdWfl_USuxuQ"    # FPT Cloud API Key
BASE_URL="https://mkp-api.fptcloud.com"  # FPT Cloud API Base URL
```

### 🤖 Model Selection
```env
LLM_MODEL="QwQ-32B"                      # Default LLM model
EMBEDDING_MODEL="Vietnamese_Embedding"   # Default embedding model
```

### ⚙️ Model Parameters
```env
MAX_TOKENS=4096        # Maximum tokens per request
TEMPERATURE=0.7        # Creativity level (0.0-2.0)
TOP_P=0.9             # Nucleus sampling (0.0-1.0)
CONTEXT_SIZE=8192     # Model context window size
```

### ⏱️ Timeout Configuration
```env
REQUEST_TIMEOUT=120    # Request timeout in seconds
VALIDATION_TIMEOUT=30  # Credential validation timeout
```

## 🎯 Parameter Explanations

### Temperature (0.0 - 2.0)
- **0.0**: Deterministic, consistent responses
- **0.7**: Balanced creativity and consistency (recommended)
- **1.0**: More creative and varied responses
- **2.0**: Highly creative but potentially inconsistent

### Top-P (0.0 - 1.0)
- **0.1**: Very focused, conservative responses
- **0.9**: Balanced diversity (recommended)
- **1.0**: Maximum diversity

### Max Tokens
- **1024**: Short responses
- **4096**: Standard responses (recommended)
- **8192**: Long, detailed responses

### Context Size
- **4096**: Standard context window
- **8192**: Extended context (recommended)
- **16384**: Maximum context (if supported)

## 🔧 Advanced Configuration

### Custom Model Parameters
Plugin sử dụng environment variables để cấu hình default values cho:

1. **LLM Model Schema**:
   - Temperature default từ `TEMPERATURE`
   - Top-P default từ `TOP_P`
   - Max tokens limit từ `MAX_TOKENS`
   - Context size từ `CONTEXT_SIZE`

2. **Embedding Model Schema**:
   - Context size từ `CONTEXT_SIZE`

3. **Timeout Settings**:
   - Client timeout từ `REQUEST_TIMEOUT`
   - Validation timeout từ `VALIDATION_TIMEOUT`

### Example Configurations

#### Conservative Setup (Consistent responses)
```env
TEMPERATURE=0.1
TOP_P=0.5
MAX_TOKENS=2048
REQUEST_TIMEOUT=60
```

#### Creative Setup (Varied responses)
```env
TEMPERATURE=1.2
TOP_P=0.95
MAX_TOKENS=4096
REQUEST_TIMEOUT=180
```

#### Production Setup (Balanced)
```env
TEMPERATURE=0.7
TOP_P=0.9
MAX_TOKENS=4096
CONTEXT_SIZE=8192
REQUEST_TIMEOUT=120
VALIDATION_TIMEOUT=30
```

## 🚀 Usage Examples

### Test with Custom Configuration
```bash
# Update .env file with your preferred settings
echo "TEMPERATURE=0.5" >> .env
echo "MAX_TOKENS=2048" >> .env

# Test the configuration
python3 test_client.py
```

### Runtime Configuration Check
```python
import os
from dotenv import load_dotenv

load_dotenv()

print(f"Temperature: {os.getenv('TEMPERATURE', '0.7')}")
print(f"Max Tokens: {os.getenv('MAX_TOKENS', '4096')}")
print(f"Timeout: {os.getenv('REQUEST_TIMEOUT', '120')}")
```

## 📊 Performance Tuning

### For Fast Responses
```env
MAX_TOKENS=1024
REQUEST_TIMEOUT=60
TEMPERATURE=0.3
```

### For Detailed Analysis
```env
MAX_TOKENS=8192
REQUEST_TIMEOUT=300
TEMPERATURE=0.7
CONTEXT_SIZE=16384
```

### For Creative Writing
```env
MAX_TOKENS=4096
TEMPERATURE=1.0
TOP_P=0.95
REQUEST_TIMEOUT=180
```

## 🔍 Troubleshooting

### Timeout Issues
- Increase `REQUEST_TIMEOUT` for complex requests
- Reduce `MAX_TOKENS` for faster responses
- Check network connectivity

### Quality Issues
- Adjust `TEMPERATURE` for consistency vs creativity
- Modify `TOP_P` for response diversity
- Increase `CONTEXT_SIZE` for better understanding

### Validation Errors
- Verify `API_KEY` is correct
- Check `BASE_URL` accessibility
- Increase `VALIDATION_TIMEOUT` if needed

## 📝 Configuration Best Practices

1. **Start with defaults** and adjust based on use case
2. **Test changes** with `test_client.py` before deployment
3. **Monitor performance** and adjust timeouts accordingly
4. **Use environment-specific** .env files for different deployments
5. **Document custom configurations** for team consistency

## 🎉 Ready to Deploy!

After configuring your environment variables:

1. **Test configuration**: `python3 test_client.py`
2. **Package plugin**: `./package_plugin.sh`
3. **Deploy to Dify**: Upload `aidibiz_fpt_cloud.signed.difypkg`
4. **Configure provider** with your API key in Dify interface

Your plugin will automatically use the configured parameters for optimal performance!
