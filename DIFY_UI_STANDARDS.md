# Dify Plugin UI Standards - Model Configuration

## 📋 Chuẩn khai báo UI cho Dify Plugin

Plugin này tuân thủ chuẩn Dify UI với các form fields sau:

### 🎛️ Model Credential Schema (UI Forms)

```yaml
model_credential_schema:
  model:
    label:
      en_US: Model Name
      vi_VN: Tên mô hình
    placeholder:
      en_US: "Enter your model name (e.g., QwQ-32B, Vietnamese_Embedding)"
      vi_VN: "Nhập tên mô hình của bạn (ví dụ: QwQ-32B, Vietnamese_Embedding)"
  
  credential_form_schemas:
  # 1. API Key (Required)
  - label:
      en_US: API Key
      vi_VN: Khóa API
    placeholder:
      en_US: Enter your FPT Cloud AI Marketplace API Key
      vi_VN: Nhập khóa API FPT Cloud AI Marketplace của bạn
    required: true
    type: secret-input
    variable: api_key

  # 2. Context Size (Manual Input)
  - label:
      en_US: Context Size
      vi_VN: <PERSON><PERSON><PERSON> thước ngữ cảnh
    placeholder:
      en_US: "Enter context size (e.g., 4096, 8192, 32768)"
      vi_VN: "Nhập kích thước ngữ cảnh (ví dụ: 4096, 8192, 32768)"
    required: false
    type: text-input
    variable: context_size
    default: "8192"

  # 3. Max Tokens Upper Bound
  - label:
      en_US: Max Tokens (Upper Bound)
      vi_VN: Số token tối đa (Giới hạn trên)
    placeholder:
      en_US: "Enter max tokens upper bound (e.g., 1024, 2048, 4096)"
      vi_VN: "Nhập giới hạn trên số token (ví dụ: 1024, 2048, 4096)"
    required: false
    type: text-input
    variable: max_tokens
    default: "4096"

  # 4. Vision Support (Yes/No Radio)
  - label:
      en_US: Vision Support
      vi_VN: Hỗ trợ Vision
    required: false
    type: radio
    variable: vision_support
    default: "no"
    options:
    - label:
        en_US: "Yes"
        vi_VN: "Có"
      value: "yes"
    - label:
        en_US: "No"
        vi_VN: "Không"
      value: "no"

  # 5. Function Call Support (Yes/No Radio)
  - label:
      en_US: Function Call Support
      vi_VN: Hỗ trợ Function Call
    required: false
    type: radio
    variable: function_call_support
    default: "no"
    options:
    - label:
        en_US: "Yes"
        vi_VN: "Có"
      value: "yes"
    - label:
        en_US: "No"
        vi_VN: "Không"
      value: "no"
```

## 🎯 Supported UI Field Types

### 1. **text-input**
- Dùng cho: Context Size, Max Tokens
- Cho phép user nhập số liệu tự do
- Có placeholder và default value

### 2. **secret-input**
- Dùng cho: API Key
- Ẩn nội dung khi nhập
- Required field

### 3. **radio**
- Dùng cho: Vision Support, Function Call Support
- Chọn Yes/No
- Default: "no"

### 4. **select** (Alternative)
- Có thể dùng thay cho radio
- Dropdown menu
- Multiple options

### 5. **switch** (Not supported in current Dify version)
- Boolean toggle
- Chưa được hỗ trợ

## 🔧 Python Code Integration

### Processing UI Values

```python
def get_customizable_model_schema(self, model: str, credentials: dict) -> Optional[AIModelEntity]:
    # Get configuration from UI forms
    context_size = int(credentials.get("context_size", "8192"))
    max_tokens_default = int(credentials.get("max_tokens", "4096"))
    
    # Process Yes/No radio buttons
    vision_support_ui = credentials.get("vision_support", "no")
    function_call_support_ui = credentials.get("function_call_support", "no")
    
    # Convert to boolean
    vision_support = vision_support_ui == "yes"
    function_calling = function_call_support_ui == "yes"
    
    # Build features list
    features = []
    if function_calling:
        features.append("tool-call")
    if vision_support:
        features.append("vision")
    
    # Build model properties
    model_properties = {
        ModelPropertyKey.MODE: LLMMode.CHAT.value,
        ModelPropertyKey.CONTEXT_SIZE: context_size,
    }
    
    if function_calling:
        model_properties[ModelPropertyKey.FUNCTION_CALLING] = True
    
    if vision_support:
        model_properties[ModelPropertyKey.VISION] = {"enabled": True}
```

## 🎨 UI Appearance in Dify

Khi user setup model trong Dify, sẽ thấy:

1. **Model Name**: Text input field
2. **API Key**: Password field (hidden)
3. **Context Size**: Number input với placeholder
4. **Max Tokens (Upper Bound)**: Number input với placeholder
5. **Vision Support**: Radio buttons (Yes/No)
6. **Function Call Support**: Radio buttons (Yes/No)

## ✅ Benefits of This Approach

1. **User-friendly**: Clear Yes/No options thay vì dropdown phức tạp
2. **Flexible**: Manual context size input thay vì auto-detect
3. **Standard**: Tuân thủ Dify UI guidelines
4. **Multilingual**: Hỗ trợ tiếng Anh và tiếng Việt
5. **Configurable**: Upper bound cho max tokens

## 🚀 Usage Examples

### Basic Text Model
```
Model Name: QwQ-32B
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: 8192
Max Tokens: 4096
Vision Support: No
Function Call Support: No
```

### Vision Model
```
Model Name: gemma-3-27b-it
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: 8192
Max Tokens: 4096
Vision Support: Yes
Function Call Support: No
```

### Full-Featured Model
```
Model Name: advanced-model
API Key: sk-HMHa6NNBNnJdWfl_USuxuQ
Context Size: 16384
Max Tokens: 4096
Vision Support: Yes
Function Call Support: Yes
```
