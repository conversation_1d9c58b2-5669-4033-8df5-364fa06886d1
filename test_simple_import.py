#!/usr/bin/env python3
"""
Simple test to check if our Cloudflare plugin files have the right structure
"""
import sys
import os

def test_cloudflare_plugin_structure():
    """Test if Cloudflare plugin has correct structure"""
    print("🔍 Testing Cloudflare Workers AI Plugin Structure...")
    
    # Check if files exist
    cloudflare_llm_path = "cloudflare-workers-ai/models/llm/llm.py"
    cloudflare_embedding_path = "cloudflare-workers-ai/models/text_embedding/text_embedding.py"
    
    if not os.path.exists(cloudflare_llm_path):
        print(f"❌ File not found: {cloudflare_llm_path}")
        return False
    
    if not os.path.exists(cloudflare_embedding_path):
        print(f"❌ File not found: {cloudflare_embedding_path}")
        return False
    
    print("✅ Plugin files found")
    
    # Check LLM file content
    print("\n📋 Checking LLM file content...")
    with open(cloudflare_llm_path, 'r') as f:
        llm_content = f.read()
    
    required_functions = [
        'def validate_credentials',
        'def get_customizable_model_schema',
        'def get_model_mode',
        'def _invoke',
        'def get_num_tokens',
        'def _num_tokens_from_messages',
        '_invoke_error_mapping',
    ]
    
    required_imports = [
        'from dify_plugin.entities.model.llm import',
        'LLMMode',
        'LLMResult',
        'LLMResultChunk',
        'LLMResultChunkDelta',
    ]
    
    missing_functions = []
    for func in required_functions:
        if func in llm_content:
            print(f"✅ {func}")
        else:
            print(f"❌ {func}")
            missing_functions.append(func)
    
    print("\n📋 Checking imports...")
    missing_imports = []
    for imp in required_imports:
        if imp in llm_content:
            print(f"✅ {imp}")
        else:
            print(f"❌ {imp}")
            missing_imports.append(imp)
    
    # Check Text Embedding file content
    print("\n📋 Checking Text Embedding file content...")
    with open(cloudflare_embedding_path, 'r') as f:
        embedding_content = f.read()
    
    embedding_functions = [
        'def validate_credentials',
        'def get_customizable_model_schema', 
        'def _invoke',
        'def get_num_tokens',
        '_invoke_error_mapping',
    ]
    
    missing_embedding_functions = []
    for func in embedding_functions:
        if func in embedding_content:
            print(f"✅ {func}")
        else:
            print(f"❌ {func}")
            missing_embedding_functions.append(func)
    
    # Summary
    print("\n" + "="*60)
    print("📊 STRUCTURE CHECK SUMMARY")
    print("="*60)
    
    if not missing_functions and not missing_imports and not missing_embedding_functions:
        print("🎉 All required functions and imports found!")
        print("✅ LLM Model: COMPLETE")
        print("✅ Text Embedding Model: COMPLETE")
        return True
    else:
        print("⚠️ Missing required items:")
        if missing_functions:
            print(f"❌ LLM missing functions: {missing_functions}")
        if missing_imports:
            print(f"❌ LLM missing imports: {missing_imports}")
        if missing_embedding_functions:
            print(f"❌ Embedding missing functions: {missing_embedding_functions}")
        return False

def check_class_names():
    """Check if class names are correct"""
    print("\n🔍 Checking class names...")
    
    # Check LLM class name
    llm_path = "cloudflare-workers-ai/models/llm/llm.py"
    with open(llm_path, 'r') as f:
        llm_content = f.read()
    
    if "class CloudflareWorkersAILanguageModel" in llm_content:
        print("✅ CloudflareWorkersAILanguageModel class found")
    else:
        print("❌ CloudflareWorkersAILanguageModel class not found")
        return False
    
    # Check Text Embedding class name
    embedding_path = "cloudflare-workers-ai/models/text_embedding/text_embedding.py"
    with open(embedding_path, 'r') as f:
        embedding_content = f.read()
    
    if "class CloudflareWorkersAITextEmbeddingModel" in embedding_content:
        print("✅ CloudflareWorkersAITextEmbeddingModel class found")
    else:
        print("❌ CloudflareWorkersAITextEmbeddingModel class not found")
        return False
    
    return True

def check_specific_implementations():
    """Check specific implementation details"""
    print("\n🔍 Checking specific implementations...")
    
    llm_path = "cloudflare-workers-ai/models/llm/llm.py"
    with open(llm_path, 'r') as f:
        llm_content = f.read()
    
    # Check if get_model_mode returns LLMMode.CHAT
    if "return LLMMode.CHAT" in llm_content:
        print("✅ get_model_mode returns LLMMode.CHAT")
    else:
        print("❌ get_model_mode does not return LLMMode.CHAT")
        return False
    
    # Check if _invoke_error_mapping is a property
    if "@property" in llm_content and "_invoke_error_mapping" in llm_content:
        print("✅ _invoke_error_mapping is a property")
    else:
        print("❌ _invoke_error_mapping is not a property")
        return False
    
    # Check if timeout configuration exists
    if "timeout_config = httpx.Timeout" in llm_content:
        print("✅ Enhanced timeout configuration found")
    else:
        print("❌ Enhanced timeout configuration not found")
        return False
    
    # Check if retry logic exists
    if "max_retries" in llm_content and "retry_delay" in llm_content:
        print("✅ Retry logic found")
    else:
        print("❌ Retry logic not found")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🚀 Cloudflare Workers AI Plugin - Simple Structure Test")
    print("=" * 60)
    
    tests = [
        ("Plugin Structure", test_cloudflare_plugin_structure),
        ("Class Names", check_class_names),
        ("Specific Implementations", check_specific_implementations),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 FINAL SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Plugin structure is correct!")
        print("✅ All required functions and imports present")
        print("✅ Ready for Dify deployment")
    else:
        print("⚠️ Plugin structure needs fixes")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
